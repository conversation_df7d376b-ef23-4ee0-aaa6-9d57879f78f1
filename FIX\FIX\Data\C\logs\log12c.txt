[735C:13CC][2025-03-02T01:33:59]: Burn v3.6.3542.0, Windows v6.3 (Build 9600: Service Pack 0), path: C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp12\cp12c.exe, cmdline: '/quiet /repair /norestart /log "C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12c.txt" -burn.unelevated BurnPipe.{8E13FA80-86F0-447C-A42A-790C5641FD5E} {509D8010-7C24-4004-9310-48D595432754} 28940'
[735C:13CC][2025-03-02T01:33:59]: Setting string variable 'WixBundleLog' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12c.txt'
[735C:13CC][2025-03-02T01:33:59]: Setting string variable 'WixBundleOriginalSource' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp12\cp12c.exe'
[735C:13CC][2025-03-02T01:33:59]: Setting string variable 'WixBundleName' to value 'Microsoft Visual C++ 2012 Redistributable (x64) - 11.0.61030'
[735C:13CC][2025-03-02T01:33:59]: Detect 2 packages
[735C:13CC][2025-03-02T01:33:59]: Detected package: vcRuntimeMinimum_x64, state: Absent, cached: None
[735C:13CC][2025-03-02T01:33:59]: Detected package: vcRuntimeAdditional_x64, state: Absent, cached: None
[735C:13CC][2025-03-02T01:33:59]: Condition 'VersionNT64 >= v6.0 OR (VersionNT64 = v5.2 AND ServicePackLevel >= 1)' evaluates to true.
[735C:13CC][2025-03-02T01:33:59]: Detect complete, result: 0x0
[735C:13CC][2025-03-02T01:33:59]: Plan 2 packages, action: Install
[735C:13CC][2025-03-02T01:33:59]: Condition 'VersionNT64 AND (VersionNT > v6.2 OR (VersionNT = v6.2 AND (NTProductType = 1)) OR (VersionNT = v6.2 AND NOT (NTProductType = 1)) OR (VersionNT = v6.0 AND NOT (NTProductType = 1)) OR (VersionNT = v6.1 AND (NTProductType = 1)) OR (VersionNT = v6.1 AND NOT (NTProductType = 1)) OR (VersionNT = v6.0 AND (NTProductType = 1)) OR (VersionNT = v5.1) OR (VersionNT = v5.2 AND NOT (NTProductType = 1)) OR (VersionNT = v5.2 AND (NTProductType = 1)))' evaluates to true.
[735C:13CC][2025-03-02T01:33:59]: Setting string variable 'WixBundleRollbackLog_vcRuntimeMinimum_x64' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12c_0_vcRuntimeMinimum_x64_rollback.txt'
[735C:13CC][2025-03-02T01:33:59]: Setting string variable 'WixBundleLog_vcRuntimeMinimum_x64' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12c_0_vcRuntimeMinimum_x64.txt'
[735C:13CC][2025-03-02T01:33:59]: Condition 'VersionNT64 AND (VersionNT > v6.2 OR (VersionNT = v6.2 AND (NTProductType = 1)) OR (VersionNT = v6.2 AND NOT (NTProductType = 1)) OR (VersionNT = v6.0 AND NOT (NTProductType = 1)) OR (VersionNT = v6.1 AND (NTProductType = 1)) OR (VersionNT = v6.1 AND NOT (NTProductType = 1)) OR (VersionNT = v6.0 AND (NTProductType = 1)) OR (VersionNT = v5.1) OR (VersionNT = v5.2 AND NOT (NTProductType = 1)) OR (VersionNT = v5.2 AND (NTProductType = 1)))' evaluates to true.
[735C:13CC][2025-03-02T01:33:59]: Setting string variable 'WixBundleRollbackLog_vcRuntimeAdditional_x64' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12c_1_vcRuntimeAdditional_x64_rollback.txt'
[735C:13CC][2025-03-02T01:33:59]: Setting string variable 'WixBundleLog_vcRuntimeAdditional_x64' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12c_1_vcRuntimeAdditional_x64.txt'
[735C:13CC][2025-03-02T01:33:59]: Planned package: vcRuntimeMinimum_x64, state: Absent, default requested: Present, ba requested: Present, execute: Install, rollback: Uninstall, cache: Yes, uncache: No, dependency: Register
[735C:13CC][2025-03-02T01:33:59]: Planned package: vcRuntimeAdditional_x64, state: Absent, default requested: Present, ba requested: Present, execute: Install, rollback: Uninstall, cache: Yes, uncache: No, dependency: Register
[735C:13CC][2025-03-02T01:33:59]: Plan complete, result: 0x0
[735C:13CC][2025-03-02T01:33:59]: Apply begin
[710C:7118][2025-03-02T01:33:59]: Creating a system restore point.
[710C:7118][2025-03-02T01:34:04]: Created a system restore point.
[710C:7118][2025-03-02T01:34:04]: Caching bundle from: 'C:\Users\<USER>\AppData\Local\Temp\{ca67548a-5ebe-413a-b50c-4b9ceb6d66c6}\.be\vcredist_x64.exe' to: 'C:\ProgramData\Package Cache\{ca67548a-5ebe-413a-b50c-4b9ceb6d66c6}\vcredist_x64.exe'
[710C:7118][2025-03-02T01:34:04]: Registering bundle dependency provider: {ca67548a-5ebe-413a-b50c-4b9ceb6d66c6}, version: 11.0.61030.0
[710C:82D8][2025-03-02T01:34:04]: Verified acquired payload: vcRuntimeMinimum_x64 at path: C:\ProgramData\Package Cache\.unverified\vcRuntimeMinimum_x64, moving to: C:\ProgramData\Package Cache\{CF2BEA3C-26EA-32F8-AA9B-331F7E34BA97}v11.0.61030\packages\vcRuntimeMinimum_amd64\vc_runtimeMinimum_x64.msi.
[710C:82D8][2025-03-02T01:34:04]: Verified acquired payload: cab5046A8AB272BF37297BB7928664C9503 at path: C:\ProgramData\Package Cache\.unverified\cab5046A8AB272BF37297BB7928664C9503, moving to: C:\ProgramData\Package Cache\{CF2BEA3C-26EA-32F8-AA9B-331F7E34BA97}v11.0.61030\packages\vcRuntimeMinimum_amd64\cab1.cab.
[710C:82D8][2025-03-02T01:34:04]: Verified acquired payload: vcRuntimeAdditional_x64 at path: C:\ProgramData\Package Cache\.unverified\vcRuntimeAdditional_x64, moving to: C:\ProgramData\Package Cache\{37B8F9C7-03FB-3253-8781-2517C99D7C00}v11.0.61030\packages\vcRuntimeAdditional_amd64\vc_runtimeAdditional_x64.msi.
[710C:82D8][2025-03-02T01:34:04]: Verified acquired payload: cab2C04DDC374BD96EB5C8EB8208F2C7C92 at path: C:\ProgramData\Package Cache\.unverified\cab2C04DDC374BD96EB5C8EB8208F2C7C92, moving to: C:\ProgramData\Package Cache\{37B8F9C7-03FB-3253-8781-2517C99D7C00}v11.0.61030\packages\vcRuntimeAdditional_amd64\cab1.cab.
[710C:7118][2025-03-02T01:34:04]: Applying execute package: vcRuntimeMinimum_x64, action: Install, path: C:\ProgramData\Package Cache\{CF2BEA3C-26EA-32F8-AA9B-331F7E34BA97}v11.0.61030\packages\vcRuntimeMinimum_amd64\vc_runtimeMinimum_x64.msi, arguments: ' MSIFASTINSTALL="7" NOVSUI="1"'
[735C:13CC][2025-03-02T01:34:04]: Applied execute package: vcRuntimeMinimum_x64, result: 0x0, restart: None
[710C:7118][2025-03-02T01:34:04]: Registering dependency: {ca67548a-5ebe-413a-b50c-4b9ceb6d66c6} on package provider: Microsoft.VS.VC_RuntimeMinimum_amd64,v11, package: vcRuntimeMinimum_x64
[710C:7118][2025-03-02T01:34:04]: Registering dependency: {ca67548a-5ebe-413a-b50c-4b9ceb6d66c6} on package provider: Microsoft.VS.VC_RuntimeMinimumVSU_amd64,v11, package: vcRuntimeMinimum_x64
[710C:7118][2025-03-02T01:34:04]: Applying execute package: vcRuntimeAdditional_x64, action: Install, path: C:\ProgramData\Package Cache\{37B8F9C7-03FB-3253-8781-2517C99D7C00}v11.0.61030\packages\vcRuntimeAdditional_amd64\vc_runtimeAdditional_x64.msi, arguments: ' MSIFASTINSTALL="7" NOVSUI="1"'
[735C:13CC][2025-03-02T01:34:04]: Applied execute package: vcRuntimeAdditional_x64, result: 0x0, restart: None
[710C:7118][2025-03-02T01:34:04]: Registering dependency: {ca67548a-5ebe-413a-b50c-4b9ceb6d66c6} on package provider: Microsoft.VS.VC_RuntimeAdditional_amd64,v11, package: vcRuntimeAdditional_x64
[710C:7118][2025-03-02T01:34:04]: Registering dependency: {ca67548a-5ebe-413a-b50c-4b9ceb6d66c6} on package provider: Microsoft.VS.VC_RuntimeAdditionalVSU_amd64,v11, package: vcRuntimeAdditional_x64
[735C:13CC][2025-03-02T01:34:04]: Apply complete, result: 0x0, restart: None, ba requested restart:  No
[735C:13CC][2025-03-02T01:34:04]: Shutting down, exit code: 0x0
[735C:13CC][2025-03-02T01:34:04]: Variable: NTProductType = 1
[735C:13CC][2025-03-02T01:34:04]: Variable: VersionNT = 6.3.0.0
[735C:13CC][2025-03-02T01:34:04]: Variable: VersionNT64 = 6.3.0.0
[735C:13CC][2025-03-02T01:34:04]: Variable: WixBundleAction = 4
[735C:13CC][2025-03-02T01:34:04]: Variable: WixBundleElevated = 1
[735C:13CC][2025-03-02T01:34:04]: Variable: WixBundleInstalled = 0
[735C:13CC][2025-03-02T01:34:04]: Variable: WixBundleLog = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12c.txt
[735C:13CC][2025-03-02T01:34:04]: Variable: WixBundleLog_vcRuntimeAdditional_x64 = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12c_1_vcRuntimeAdditional_x64.txt
[735C:13CC][2025-03-02T01:34:04]: Variable: WixBundleLog_vcRuntimeMinimum_x64 = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12c_0_vcRuntimeMinimum_x64.txt
[735C:13CC][2025-03-02T01:34:04]: Variable: WixBundleName = Microsoft Visual C++ 2012 Redistributable (x64) - 11.0.61030
[735C:13CC][2025-03-02T01:34:04]: Variable: WixBundleOriginalSource = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp12\cp12c.exe
[735C:13CC][2025-03-02T01:34:04]: Variable: WixBundleProviderKey = {ca67548a-5ebe-413a-b50c-4b9ceb6d66c6}
[735C:13CC][2025-03-02T01:34:04]: Variable: WixBundleRollbackLog_vcRuntimeAdditional_x64 = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12c_1_vcRuntimeAdditional_x64_rollback.txt
[735C:13CC][2025-03-02T01:34:04]: Variable: WixBundleRollbackLog_vcRuntimeMinimum_x64 = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12c_0_vcRuntimeMinimum_x64_rollback.txt
[735C:13CC][2025-03-02T01:34:04]: Variable: WixBundleTag = 
[735C:13CC][2025-03-02T01:34:04]: Variable: WixBundleVersion = 11.0.61030.0
[735C:13CC][2025-03-02T01:34:04]: Exit code: 0x0, restarting: No
