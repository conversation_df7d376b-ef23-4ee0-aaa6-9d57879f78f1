@echo off
echo ===================================================
echo Automatic Virtualization Enable Script
echo ===================================================

REM Check for Administrator privileges
NET SESSION >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Requesting Administrator privileges...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

echo Running with Administrator privileges...
echo.

echo [+] Attempting to enable virtualization features...

REM Enable Hyper-V and virtualization features
echo [+] Enabling Windows virtualization features...
dism /online /enable-feature /featurename:Microsoft-Hyper-V-All /all /norestart
dism /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
dism /online /enable-feature /featurename:HypervisorPlatform /all /norestart

REM Enable virtualization in registry
echo [+] Setting virtualization registry entries...
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\OptionalFeatures\Microsoft-Hyper-V-Hypervisor" /v "Enabled" /t REG_DWORD /d 1 /f
reg add "HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard" /v "RequirePlatformSecurityFeatures" /t REG_DWORD /d 1 /f

REM Boot configuration for virtualization
echo [+] Configuring boot settings for virtualization...
bcdedit /set hypervisorlaunchtype auto
bcdedit /set {current} hypervisorlaunchtype auto

REM Intel VT-x specific settings
echo [+] Applying Intel VT-x optimizations...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Power" /v "PlatformAoAcOverride" /t REG_DWORD /d 1 /f

echo.
echo [+] Virtualization enable script completed!
echo [!] RESTART REQUIRED for changes to take effect.
echo [!] If virtualization still shows disabled after restart,
echo [!] you may need to enable Intel VT-x in BIOS manually.
echo.
pause
