@echo off
echo ===================================================
echo Complete Auto-Fix for Progress Issues
echo ===================================================

REM Check for Administrator privileges
NET SESSION >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Requesting Administrator privileges...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

echo Running with Administrator privileges...
echo.

echo [+] Starting complete auto-fix for all Progress issues...
echo.

REM Fix 1: Disable Firewall (both profiles)
echo [1/6] Disabling Windows Firewall...
netsh advfirewall set privateprofile state off
netsh advfirewall set publicprofile state off
netsh advfirewall set domainprofile state off
echo [+] Firewall disabled

REM Fix 2: Disable SmartScreen (Check Apps and Files)
echo [2/6] Disabling SmartScreen...
reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\System" /v "EnableSmartScreen" /t REG_DWORD /d 0 /f
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "SmartScreenEnabled" /t REG_SZ /d "Off" /f
reg add "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\AppHost" /v "EnableWebContentEvaluation" /t REG_DWORD /d 0 /f
echo [+] SmartScreen disabled

REM Fix 3: Secure Boot bypass (software level)
echo [3/6] Applying Secure Boot bypass...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\SecureBoot\State" /v "UEFISecureBootEnabled" /t REG_DWORD /d 0 /f
bcdedit /set testsigning on
bcdedit /set nointegritychecks on
echo [+] Secure Boot bypass applied

REM Fix 4: Enable virtualization features
echo [4/6] Enabling virtualization features...
dism /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
dism /online /enable-feature /featurename:HypervisorPlatform /all /norestart
bcdedit /set hypervisorlaunchtype auto
echo [+] Virtualization features enabled

REM Fix 5: Disable all security features
echo [5/6] Disabling security features...
reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows Defender" /v "DisableAntiSpyware" /t REG_DWORD /d 1 /f
reg add "HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard" /v "EnableVirtualizationBasedSecurity" /t REG_DWORD /d 0 /f
reg add "HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios\HypervisorEnforcedCodeIntegrity" /v "Enabled" /t REG_DWORD /d 0 /f
echo [+] Security features disabled

REM Fix 6: Apply signature bypass
echo [6/6] Applying signature bypass...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\CI\Policy" /v "VerifiedAndReputablePolicyState" /t REG_DWORD /d 0 /f
reg add "HKLM\SYSTEM\CurrentControlSet\Control\CI\Config" /v "VulnerableDriverBlocklistEnable" /t REG_DWORD /d 0 /f
echo [+] Signature bypass applied

echo.
echo ===================================================
echo AUTO-FIX COMPLETE!
echo ===================================================
echo.
echo [+] FIXED AUTOMATICALLY:
echo     - Firewall disabled (both profiles)
echo     - SmartScreen disabled
echo     - Secure Boot bypass applied
echo     - Virtualization features enabled
echo     - Security features disabled
echo     - Signature bypass applied
echo.
echo [!] MANUAL BIOS FIXES STILL NEEDED:
echo     - Enable Intel VT-x in BIOS
echo     - Disable Secure Boot in BIOS
echo.
echo [?] Would you like to restart to BIOS now? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo [+] Restarting to BIOS in 10 seconds...
    shutdown /r /fw /t 10 /c "Restarting to BIOS for manual fixes"
) else (
    echo [+] Manual restart required later for changes to take effect.
)
echo.
pause
