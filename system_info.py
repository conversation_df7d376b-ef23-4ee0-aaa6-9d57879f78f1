import wmi
import uuid
import platform
import socket

c = wmi.WMI()
bios_info = c.Win32_BIOS()[0]
board_info = c.Win32_BaseBoard()[0]

info = {
    "hostname": socket.gethostname(),
    "UUID": str(uuid.getnode()),
    "BIOSSerial": bios_info.SerialNumber.strip(),
    "BoardManufacturer": board_info.Manufacturer.strip(),
    "BoardProduct": board_info.Product.strip(),
    "OS": platform.platform(),
    "System": platform.system()
}

print("==== YOUR SYSTEM IDENTITY ====")
for k, v in info.items():
    print(f"{k}: {v}")
