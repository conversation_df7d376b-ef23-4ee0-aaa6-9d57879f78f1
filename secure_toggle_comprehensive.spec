# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['secure_toggle_comprehensive.pyw'],
    pathex=[],
    binaries=[],
    datas=[
        ('secure_toggle_lock_config.json', '.'),
    ],
    hiddenimports=[
        'wmi',
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'winreg',
        'uuid',
        'platform',
        'ctypes',
        'subprocess',
        'json',
        'pathlib',
        'threading',
        'time',
        'os',
        'shutil'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SecureToggleComprehensive',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    uac_admin=True,  # Request admin privileges
    icon=None,  # Add icon path here if you have one
)
