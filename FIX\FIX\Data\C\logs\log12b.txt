[4E88:7170][2025-03-02T01:34:08]: Burn v3.6.3542.0, Windows v6.3 (Build 9600: Service Pack 0), path: C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp12\cp12b.exe, cmdline: '/quiet /repair /norestart /log "C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12b.txt" -burn.unelevated BurnPipe.{EC14D497-9B7B-49F7-88D5-29491812E08A} {1DAFAF24-9122-4E62-8B20-E35555974D61} 33240'
[4E88:7170][2025-03-02T01:34:08]: Setting string variable 'WixBundleLog' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12b.txt'
[4E88:7170][2025-03-02T01:34:08]: Setting string variable 'WixBundleOriginalSource' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp12\cp12b.exe'
[4E88:7170][2025-03-02T01:34:08]: Detect 2 packages
[4E88:7170][2025-03-02T01:34:08]: Detected package: vcRuntimeMinimum_x86, state: Present, cached: Complete
[4E88:7170][2025-03-02T01:34:08]: Detected package: vcRuntimeAdditional_x86, state: Present, cached: Complete
[4E88:7170][2025-03-02T01:34:08]: Condition 'VersionNT >= v6.0 OR (VersionNT = v5.1 AND ServicePackLevel >= 2) OR (VersionNT = v5.2 AND ServicePackLevel >= 1)' evaluates to true.
[4E88:7170][2025-03-02T01:34:08]: Detect complete, result: 0x0
[4E88:7170][2025-03-02T01:34:08]: Plan 2 packages, action: Repair
[4E88:7170][2025-03-02T01:34:08]: Condition 'VersionNT > v6.2 OR (VersionNT = v6.2 AND (NTProductType = 1)) OR (VersionNT = v6.2 AND NOT (NTProductType = 1)) OR (VersionNT = v6.0 AND NOT (NTProductType = 1)) OR (VersionNT = v6.1 AND (NTProductType = 1)) OR (VersionNT = v6.1 AND NOT (NTProductType = 1)) OR (VersionNT = v6.0 AND (NTProductType = 1)) OR (VersionNT = v5.1) OR (VersionNT = v5.2 AND NOT (NTProductType = 1)) OR (VersionNT = v5.2 AND (NTProductType = 1))' evaluates to true.
[4E88:7170][2025-03-02T01:34:08]: Setting string variable 'WixBundleLog_vcRuntimeMinimum_x86' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12b_0_vcRuntimeMinimum_x86.txt'
[4E88:7170][2025-03-02T01:34:08]: Condition 'VersionNT > v6.2 OR (VersionNT = v6.2 AND (NTProductType = 1)) OR (VersionNT = v6.2 AND NOT (NTProductType = 1)) OR (VersionNT = v6.0 AND NOT (NTProductType = 1)) OR (VersionNT = v6.1 AND (NTProductType = 1)) OR (VersionNT = v6.1 AND NOT (NTProductType = 1)) OR (VersionNT = v6.0 AND (NTProductType = 1)) OR (VersionNT = v5.1) OR (VersionNT = v5.2 AND NOT (NTProductType = 1)) OR (VersionNT = v5.2 AND (NTProductType = 1))' evaluates to true.
[4E88:7170][2025-03-02T01:34:08]: Setting string variable 'WixBundleLog_vcRuntimeAdditional_x86' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12b_1_vcRuntimeAdditional_x86.txt'
[4E88:7170][2025-03-02T01:34:08]: Planned package: vcRuntimeMinimum_x86, state: Present, default requested: Repair, ba requested: Repair, execute: Repair, rollback: None, cache: No, uncache: No, dependency: Register
[4E88:7170][2025-03-02T01:34:08]: Planned package: vcRuntimeAdditional_x86, state: Present, default requested: Repair, ba requested: Repair, execute: Repair, rollback: None, cache: No, uncache: No, dependency: Register
[4E88:7170][2025-03-02T01:34:08]: Plan complete, result: 0x0
[4E88:7170][2025-03-02T01:34:08]: Apply begin
[81D8:5BD4][2025-03-02T01:34:08]: Creating a system restore point.
[81D8:5BD4][2025-03-02T01:34:08]: Created a system restore point.
[81D8:1F44][2025-03-02T01:34:08]: Verified existing payload: vcRuntimeMinimum_x86 at path: C:\ProgramData\Package Cache\{BD95A8CD-1D9F-35AD-981A-3E7925026EBB}v11.0.61030\packages\vcRuntimeMinimum_x86\vc_runtimeMinimum_x86.msi.
[81D8:1F44][2025-03-02T01:34:08]: Verified existing payload: cab54A5CABBE7274D8A22EB58060AAB7623 at path: C:\ProgramData\Package Cache\{BD95A8CD-1D9F-35AD-981A-3E7925026EBB}v11.0.61030\packages\vcRuntimeMinimum_x86\cab1.cab.
[81D8:1F44][2025-03-02T01:34:08]: Verified existing payload: vcRuntimeAdditional_x86 at path: C:\ProgramData\Package Cache\{B175520C-86A2-35A7-8619-86DC379688B9}v11.0.61030\packages\vcRuntimeAdditional_x86\vc_runtimeAdditional_x86.msi.
[81D8:1F44][2025-03-02T01:34:08]: Verified existing payload: cabB3E1576D1FEFBB979E13B1A5379E0B16 at path: C:\ProgramData\Package Cache\{B175520C-86A2-35A7-8619-86DC379688B9}v11.0.61030\packages\vcRuntimeAdditional_x86\cab1.cab.
[81D8:5BD4][2025-03-02T01:34:08]: Applying execute package: vcRuntimeMinimum_x86, action: Repair, path: C:\ProgramData\Package Cache\{BD95A8CD-1D9F-35AD-981A-3E7925026EBB}v11.0.61030\packages\vcRuntimeMinimum_x86\vc_runtimeMinimum_x86.msi, arguments: ' MSIFASTINSTALL="7" NOVSUI="1"'
[4E88:7170][2025-03-02T01:34:08]: Applied execute package: vcRuntimeMinimum_x86, result: 0x0, restart: None
[81D8:5BD4][2025-03-02T01:34:08]: Registering dependency: {33d1fd90-4274-48a1-9bc1-97e33d9c2d6f} on package provider: Microsoft.VS.VC_RuntimeMinimum_x86,v11, package: vcRuntimeMinimum_x86
[81D8:5BD4][2025-03-02T01:34:08]: Registering dependency: {33d1fd90-4274-48a1-9bc1-97e33d9c2d6f} on package provider: Microsoft.VS.VC_RuntimeMinimumVSU_x86,v11, package: vcRuntimeMinimum_x86
[81D8:5BD4][2025-03-02T01:34:08]: Applying execute package: vcRuntimeAdditional_x86, action: Repair, path: C:\ProgramData\Package Cache\{B175520C-86A2-35A7-8619-86DC379688B9}v11.0.61030\packages\vcRuntimeAdditional_x86\vc_runtimeAdditional_x86.msi, arguments: ' MSIFASTINSTALL="7" NOVSUI="1"'
[4E88:7170][2025-03-02T01:34:09]: Applied execute package: vcRuntimeAdditional_x86, result: 0x0, restart: None
[81D8:5BD4][2025-03-02T01:34:09]: Registering dependency: {33d1fd90-4274-48a1-9bc1-97e33d9c2d6f} on package provider: Microsoft.VS.VC_RuntimeAdditional_x86,v11, package: vcRuntimeAdditional_x86
[81D8:5BD4][2025-03-02T01:34:09]: Registering dependency: {33d1fd90-4274-48a1-9bc1-97e33d9c2d6f} on package provider: Microsoft.VS.VC_RuntimeAdditionalVSU_x86,v11, package: vcRuntimeAdditional_x86
[4E88:7170][2025-03-02T01:34:09]: Apply complete, result: 0x0, restart: None, ba requested restart:  No
[4E88:7170][2025-03-02T01:34:09]: Shutting down, exit code: 0x0
[4E88:7170][2025-03-02T01:34:09]: Variable: NTProductType = 1
[4E88:7170][2025-03-02T01:34:09]: Variable: VersionNT = 6.3.0.0
[4E88:7170][2025-03-02T01:34:09]: Variable: WixBundleAction = 6
[4E88:7170][2025-03-02T01:34:09]: Variable: WixBundleElevated = 1
[4E88:7170][2025-03-02T01:34:09]: Variable: WixBundleInstalled = 1
[4E88:7170][2025-03-02T01:34:09]: Variable: WixBundleLog = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12b.txt
[4E88:7170][2025-03-02T01:34:09]: Variable: WixBundleLog_vcRuntimeAdditional_x86 = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12b_1_vcRuntimeAdditional_x86.txt
[4E88:7170][2025-03-02T01:34:09]: Variable: WixBundleLog_vcRuntimeMinimum_x86 = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log12b_0_vcRuntimeMinimum_x86.txt
[4E88:7170][2025-03-02T01:34:09]: Variable: WixBundleName = Microsoft Visual C++ 2012 Redistributable (x86) - 11.0.61030
[4E88:7170][2025-03-02T01:34:09]: Variable: WixBundleOriginalSource = C:\Program Files\Electronic Arts\EA Desktop\VC\vc_redist-11.0.61030.x86.exe
[4E88:7170][2025-03-02T01:34:09]: Variable: WixBundleProviderKey = {33d1fd90-4274-48a1-9bc1-97e33d9c2d6f}
[4E88:7170][2025-03-02T01:34:09]: Variable: WixBundleTag = 
[4E88:7170][2025-03-02T01:34:09]: Variable: WixBundleVersion = 11.0.61030.0
[4E88:7170][2025-03-02T01:34:09]: Exit code: 0x0, restarting: No
