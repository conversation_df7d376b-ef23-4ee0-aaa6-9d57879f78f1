# SecureToggle Comprehensive - Complete Progress Setup Tool

A hardware-locked GUI application for comprehensive Windows security management, system optimization, and Progress software environment setup. This tool combines all functionality from the Progress setup batch scripts into a user-friendly interface.

## 🔐 System Lock

This application is **hardware-locked** and will only run on the authorized system:
- **Hostname**: ATLAS
- **UUID**: 9619534469621
- **BIOS**: American Megatrends Inc. 3010, 12/17/2023
- **Motherboard**: ASUSTeK COMPUTER INC. - PRIME B550M-A AC D4
- **Processor**: 13th Gen Intel(R) Core(TM) i5-13400F
- **OS**: Microsoft Windows 11 Home
- **Memory**: 16.3 GB

## 🚀 Comprehensive Features

### **Security Management Tab**
- **Real-time Status Display**: Shows current status with color coding
  - 🟢 Green: Enabled
  - 🔴 Red: Disabled
  - 🟠 Orange: Error/Unknown
- **Security Features**:
  - Windows Defender (Real-time Protection)
  - Virtualization-based Security (VBS)
  - Hypervisor Enforced Code Integrity (HVCI)
  - Windows Update Control
  - Windows Firewall Toggle
  - User Account Control (UAC)
- **Bulk Operations**: Enable/Disable all security features at once
- **Gaming/Injection Setup**: One-click optimal configuration for Progress/Auth_PG
- **Security Recommendations**: Clear guidance on what to disable and why

### **System Optimization Tab**
- **Windows Services**: Disable/restore unnecessary services
- **Performance Tweaks**: Registry optimizations for better performance
- **Gaming Optimizations**:
  - Disable Game DVR
  - Disable fullscreen optimizations
  - Set high performance power plan
- **Operation Logging**: Real-time log of all optimization actions

### **Progress Environment Tab**
- **Directory Creation**: Automatic Progress software directory structure
- **Environment Variables**: Set PROGRESS_DLC and PATH variables
- **Registry Setup**: Create Progress OpenEdge registry entries
- **Configuration Files**: Generate progress.cfg and config.ini
- **Complete Setup**: One-click full Progress environment setup

### **Installation Tools Tab**
- **System Dependencies**:
  - DirectX Web Installer
  - Visual C++ Redistributables (x64 & x86)
- **System Maintenance**:
  - Temporary file cleanup
  - System File Checker (SFC) scan
  - **Scan Browsers** - Detect currently running browser processes
  - **Close All Browsers** - Terminate all browser processes (50+ browsers supported)
- **Installation Logging**: Detailed progress tracking

### **Auth_PG Integration Tab** ⭐ **NEW**
- **Authentication Status**: Real-time Auth_PG v5 status monitoring
- **Driver Management**:
  - Extract Driver Support One.rar automatically
  - Install Auth_PG drivers with multiple fallback methods
  - Apply driver fixes from FIX.rar when installation fails
  - Scan running browsers and close all browsers (useful for troubleshooting)
- **Authentication Control**:
  - Launch Auth_PG v5 executable
  - View visual instructions (change-2-eng.JPG)
  - Access Progress instructions (securecheats.com)
  - **Auto-Login Forum Access** with your credentials (<EMAIL>)
- **Error Handling & Support**:
  - Automatic detection of "Driver Installation Failed" errors
  - **Invalider Error** detection and troubleshooting
  - **Signature Checksum Failed** comprehensive fix
  - Direct links to forum support (securedsupportone.com)
  - Step-by-step troubleshooting guidance
  - Real-time Auth_PG process monitoring
- **USB Security**: Optional USB-based authentication (not required)
- **Complete Integration**: One-click Progress + Auth_PG setup
- **Dedicated Logging**: Separate Auth_PG operations log with detailed error reporting

## 🚀 Installation & Usage

### Prerequisites
- Windows 10/11
- Python 3.7+ (for development)
- Administrator privileges

### Running from Source

**Basic Version:**
```bash
# Install dependencies
pip install -r requirements.txt

# Run basic version
python secure_toggle_gui.pyw
```

**Comprehensive Version:**
```bash
# Run comprehensive version with admin privileges
run_comprehensive_admin.bat
```

### Building Executable

**Basic Version:**
```bash
pyinstaller secure_toggle.spec
# Creates: dist/SecureToggle.exe
```

**Comprehensive Version:**
```bash
pyinstaller secure_toggle_comprehensive.spec
# Creates: dist/SecureToggleComprehensive.exe
```

## ⚠️ Important Notes

- **Administrator Required**: This application must be run with administrator privileges
- **Restart Required**: Changes to VBS and HVCI require a system restart to take effect
- **Hardware Lock**: The application will only run on the authorized system (ATLAS)
- **Registry Changes**: The application modifies Windows registry to toggle security features

## 📋 **Auth_PG Specific Instructions**

- **Follow SecureCheats Instructions**: Always follow the official instructions at https://securecheats.com/progress-instructions/ (Password: progressme)
- **Driver Installation Issues**: If you encounter "Driver Installation Failed" errors, use the "Get Help" button for forum support
- **Invalider Error**: If you see "Invalider Error", use the dedicated "Invalider Error" button for specific troubleshooting
- **USB Security**: USB security is optional but NOT required for Auth_PG to function
- **Forum Support**: Register at https://securedsupportone.com/forum/ for technical support
- **Multiple Installation Methods**: The application tries multiple driver installation methods automatically
- **Real-time Monitoring**: The application monitors Auth_PG process for errors and provides automatic assistance
- **Auto-Login Support**: Automatic credential filling for SecureCheats forum access (<EMAIL> / Stopit8008!)

## 🎮 **Security Configuration for Gaming/Injection**

### **What Actually Needs to be Disabled:**

**CRITICAL (Must Disable for Progress/Auth_PG):**
- ✅ **Windows Defender** - Blocks injection and cheat processes
- ✅ **VBS (Virtualization-based Security)** - Prevents memory injection techniques
- ✅ **HVCI (Memory Integrity)** - Blocks code injection and modification

**RECOMMENDED (Reduces Interference):**
- ✅ **Windows Firewall** - Can block network connections and processes
- ✅ **UAC (User Account Control)** - Reduces permission prompts and restrictions

**OPTIONAL (Prevents Re-enabling):**
- ⚠️ **Windows Update** - Stops Windows from automatically re-enabling security features

### **Gaming/Injection Setup Button:**
The application includes a **"Gaming/Injection Setup"** button that:
- Automatically disables critical security features
- Asks about recommended features
- Provides clear explanations for each choice
- Optimizes your system for Progress software and Auth_PG

## 🔧 Technical Details

### Security Features Managed

1. **Windows Defender**
   - Registry: `HKLM\SOFTWARE\Policies\Microsoft\Windows Defender`
   - Status Check: WMI AntiVirusProduct + Registry fallback

2. **Virtualization-based Security (VBS)**
   - Registry: `HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard`
   - Key: `EnableVirtualizationBasedSecurity`

3. **HVCI (Memory Integrity)**
   - Registry: `HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios\HypervisorEnforcedCodeIntegrity`
   - Key: `Enabled`

### Auth_PG Integration Features

1. **Driver Management**
   - Automatic extraction of Driver Support One.rar
   - Installation of .inf, .sys, .exe, and .msi driver files
   - Automatic application of fixes from FIX.rar

2. **Authentication Control**
   - Launch Auth_PG v5.exe with proper working directory
   - Visual instruction display (change-2-eng.JPG)
   - USB security toggle (optional)

3. **Complete Integration Workflow**
   - Progress environment setup
   - System optimizations
   - Auth_PG driver installation
   - Automatic driver fixes
   - Status verification

### Browser Management Features

**Comprehensive Browser Support (50+ browsers):**

**Major Browsers:**
- Google Chrome, Firefox, Microsoft Edge (all versions)
- Internet Explorer, Safari, Opera (including Opera GX)

**Alternative Browsers:**
- Brave, Vivaldi, Waterfox, Pale Moon, Chromium
- Yandex, Maxthon, SlimJet, Comodo Dragon, Torch, SeaMonkey

**Gaming/Specialized:**
- Discord, Steam, Epic Games Launcher, Battle.net (browser engines)

**Development Browsers:**
- Edge Dev/Beta/Canary, Chrome Dev/Beta/Canary
- Firefox Developer Edition/Nightly

**International Browsers:**
- UC Browser, 360 Secure Browser, Sogou, Liebao, QQ Browser, Baidu

**Advanced Features:**
- **Browser Scanning**: Detect and list all running browser processes
- **Smart Detection**: Intelligent process identification and verification
- **Graceful Closing**: Attempts graceful close first, force close as fallback
- **Detailed Logging**: Comprehensive logging of all browser operations
- **Driver Support**: Browser automation drivers (ChromeDriver, GeckoDriver, etc.)
- **Emulator Support**: Mobile browser emulators (BlueStacks, Nox, LDPlayer)
- **Real-time Feedback**: Live status updates and operation summaries

### System Lock Verification
The application verifies the following system characteristics:
- Hostname (ATLAS)
- Network adapter UUID
- BIOS information
- Motherboard details
- Processor information

## 📁 Files

### **Core Applications**
- `secure_toggle_gui.pyw` - Basic GUI application (original)
- `secure_toggle_comprehensive.pyw` - **Comprehensive GUI application (recommended)**
- `secure_toggle_lock_config.json` - Hardware lock configuration

### **Launchers**
- `run_securetoggle_admin.bat` - Admin launcher for basic version
- `run_comprehensive_admin.bat` - **Admin launcher for comprehensive version**

### **Build Files**
- `secure_toggle.spec` - PyInstaller spec for basic version
- `secure_toggle_comprehensive.spec` - PyInstaller spec for comprehensive version
- `build_securetoggle.bat` - Build script for basic version

### **Setup & Documentation**
- `progress_setup_comprehensive.bat` - Original batch script (reference)
- `requirements.txt` - Python dependencies
- `README.md` - This documentation

## 🎯 **Recommended Usage**

For the complete Progress setup experience, use:
```bash
run_comprehensive_admin.bat
```

This launches `secure_toggle_comprehensive.pyw` with all features including:
- All security toggles
- System optimizations
- Progress environment setup
- Installation tools
- Comprehensive logging

## 🛡️ Security Considerations

- The application is designed to be hardware-locked for security
- Registry modifications require administrator privileges
- Changes affect system-wide security settings
- Use responsibly and understand the implications of disabling security features

## 📝 License

This tool is for authorized use only on the specified hardware configuration.
