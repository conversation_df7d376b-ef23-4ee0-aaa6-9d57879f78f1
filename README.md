# SecureToggle Comprehensive - Complete Progress Setup Tool

A hardware-locked GUI application for comprehensive Windows security management, system optimization, and Progress software environment setup. This tool combines all functionality from the Progress setup batch scripts into a user-friendly interface.

## 🔐 System Lock

This application is **hardware-locked** and will only run on the authorized system:
- **Hostname**: ATLAS
- **UUID**: 9619534469621
- **BIOS**: American Megatrends Inc. 3010, 12/17/2023
- **Motherboard**: ASUSTeK COMPUTER INC. - PRIME B550M-A AC D4
- **Processor**: 13th Gen Intel(R) Core(TM) i5-13400F
- **OS**: Microsoft Windows 11 Home
- **Memory**: 16.3 GB

## 🚀 Comprehensive Features

### **Security Management Tab**
- **Real-time Status Display**: Shows current status with color coding
  - 🟢 Green: Enabled
  - 🔴 Red: Disabled
  - 🟠 Orange: Error/Unknown
- **Security Features**:
  - Windows Defender (Real-time Protection)
  - Virtualization-based Security (VBS)
  - Hypervisor Enforced Code Integrity (HVCI)
  - Windows Update Control
  - Windows Firewall Toggle
  - User Account Control (UAC)
- **Bulk Operations**: Enable/Disable all security features at once

### **System Optimization Tab**
- **Windows Services**: Disable/restore unnecessary services
- **Performance Tweaks**: Registry optimizations for better performance
- **Gaming Optimizations**:
  - Disable Game DVR
  - Disable fullscreen optimizations
  - Set high performance power plan
- **Operation Logging**: Real-time log of all optimization actions

### **Progress Environment Tab**
- **Directory Creation**: Automatic Progress software directory structure
- **Environment Variables**: Set PROGRESS_DLC and PATH variables
- **Registry Setup**: Create Progress OpenEdge registry entries
- **Configuration Files**: Generate progress.cfg and config.ini
- **Complete Setup**: One-click full Progress environment setup

### **Installation Tools Tab**
- **System Dependencies**:
  - DirectX Web Installer
  - Visual C++ Redistributables (x64 & x86)
- **System Maintenance**:
  - Temporary file cleanup
  - System File Checker (SFC) scan
- **Installation Logging**: Detailed progress tracking

## 🚀 Installation & Usage

### Prerequisites
- Windows 10/11
- Python 3.7+ (for development)
- Administrator privileges

### Running from Source

**Basic Version:**
```bash
# Install dependencies
pip install -r requirements.txt

# Run basic version
python secure_toggle_gui.pyw
```

**Comprehensive Version:**
```bash
# Run comprehensive version with admin privileges
run_comprehensive_admin.bat
```

### Building Executable

**Basic Version:**
```bash
pyinstaller secure_toggle.spec
# Creates: dist/SecureToggle.exe
```

**Comprehensive Version:**
```bash
pyinstaller secure_toggle_comprehensive.spec
# Creates: dist/SecureToggleComprehensive.exe
```

## ⚠️ Important Notes

- **Administrator Required**: This application must be run with administrator privileges
- **Restart Required**: Changes to VBS and HVCI require a system restart to take effect
- **Hardware Lock**: The application will only run on the authorized system
- **Registry Changes**: The application modifies Windows registry to toggle security features

## 🔧 Technical Details

### Security Features Managed

1. **Windows Defender**
   - Registry: `HKLM\SOFTWARE\Policies\Microsoft\Windows Defender`
   - Status Check: WMI AntiVirusProduct + Registry fallback

2. **Virtualization-based Security (VBS)**
   - Registry: `HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard`
   - Key: `EnableVirtualizationBasedSecurity`

3. **HVCI (Memory Integrity)**
   - Registry: `HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios\HypervisorEnforcedCodeIntegrity`
   - Key: `Enabled`

### System Lock Verification
The application verifies the following system characteristics:
- Hostname
- Network adapter UUID
- Additional hardware fingerprints (configurable)

## 📁 Files

### **Core Applications**
- `secure_toggle_gui.pyw` - Basic GUI application (original)
- `secure_toggle_comprehensive.pyw` - **Comprehensive GUI application (recommended)**
- `secure_toggle_lock_config.json` - Hardware lock configuration

### **Launchers**
- `run_securetoggle_admin.bat` - Admin launcher for basic version
- `run_comprehensive_admin.bat` - **Admin launcher for comprehensive version**

### **Build Files**
- `secure_toggle.spec` - PyInstaller spec for basic version
- `secure_toggle_comprehensive.spec` - PyInstaller spec for comprehensive version
- `build_securetoggle.bat` - Build script for basic version

### **Setup & Documentation**
- `progress_setup_comprehensive.bat` - Original batch script (reference)
- `requirements.txt` - Python dependencies
- `README.md` - This documentation

## 🎯 **Recommended Usage**

For the complete Progress setup experience, use:
```bash
run_comprehensive_admin.bat
```

This launches `secure_toggle_comprehensive.pyw` with all features including:
- All security toggles
- System optimizations
- Progress environment setup
- Installation tools
- Comprehensive logging

## 🛡️ Security Considerations

- The application is designed to be hardware-locked for security
- Registry modifications require administrator privileges
- Changes affect system-wide security settings
- Use responsibly and understand the implications of disabling security features

## 📝 License

This tool is for authorized use only on the specified hardware configuration.
