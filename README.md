# SecureToggle - Windows Security Protection Toggle Tool

A hardware-locked GUI application for toggling Windows security features including Windows Defender, VBS (Virtualization-based Security), and HVCI (Hypervisor Enforced Code Integrity).

## 🔐 System Lock

This application is **hardware-locked** and will only run on the authorized system:
- **Hostname**: ATLAS
- **UUID**: 9619534469621
- **BIOS Serial**: System Serial Number
- **Motherboard**: ASUSTeK COMPUTER INC. - PRIME B660M-A AC D4
- **OS**: Windows 11 Build 26100

## 📋 Features

- **Real-time Status Display**: Shows current status of security features with color coding
  - 🟢 Green: Enabled
  - 🔴 Red: Disabled
  - 🟠 Orange: Error/Unknown
- **Toggle Security Features**:
  - Windows Defender (Real-time Protection)
  - Virtualization-based Security (VBS)
  - Hypervisor Enforced Code Integrity (HVCI)
- **Hardware Binding**: Locked to specific machine fingerprint
- **Admin Elevation**: Automatically requests administrator privileges

## 🚀 Installation & Usage

### Prerequisites
- Windows 10/11
- Python 3.7+ (for development)
- Administrator privileges

### Running from Source
1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the application:
   ```bash
   python secure_toggle_gui.pyw
   ```

### Building Executable
1. Install PyInstaller:
   ```bash
   pip install pyinstaller
   ```

2. Build the executable:
   ```bash
   pyinstaller secure_toggle.spec
   ```

3. The executable will be created in `dist/SecureToggle.exe`

## ⚠️ Important Notes

- **Administrator Required**: This application must be run with administrator privileges
- **Restart Required**: Changes to VBS and HVCI require a system restart to take effect
- **Hardware Lock**: The application will only run on the authorized system
- **Registry Changes**: The application modifies Windows registry to toggle security features

## 🔧 Technical Details

### Security Features Managed

1. **Windows Defender**
   - Registry: `HKLM\SOFTWARE\Policies\Microsoft\Windows Defender`
   - Status Check: WMI AntiVirusProduct + Registry fallback

2. **Virtualization-based Security (VBS)**
   - Registry: `HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard`
   - Key: `EnableVirtualizationBasedSecurity`

3. **HVCI (Memory Integrity)**
   - Registry: `HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios\HypervisorEnforcedCodeIntegrity`
   - Key: `Enabled`

### System Lock Verification
The application verifies the following system characteristics:
- Hostname
- Network adapter UUID
- Additional hardware fingerprints (configurable)

## 📁 Files

- `secure_toggle_gui.pyw` - Main GUI application
- `secure_toggle_lock_config.json` - Hardware lock configuration
- `secure_toggle.spec` - PyInstaller build specification
- `requirements.txt` - Python dependencies
- `README.md` - This documentation

## 🛡️ Security Considerations

- The application is designed to be hardware-locked for security
- Registry modifications require administrator privileges
- Changes affect system-wide security settings
- Use responsibly and understand the implications of disabling security features

## 📝 License

This tool is for authorized use only on the specified hardware configuration.
