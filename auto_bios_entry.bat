@echo off
echo ===================================================
echo Automatic BIOS Entry Script
echo ===================================================

REM Check for Administrator privileges
NET SESSION >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Requesting Administrator privileges...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

echo Running with Administrator privileges...
echo.

echo [+] Setting up automatic BIOS entry...

REM Method 1: Use shutdown command to restart to UEFI
echo [+] Method 1: Restart to UEFI firmware settings...
shutdown /r /fw /t 60 /c "Restarting to BIOS/UEFI settings in 60 seconds. Press any key to cancel."

echo.
echo [!] Your computer will restart to BIOS/UEFI in 60 seconds!
echo [!] In BIOS, you need to:
echo [!] 1. Find "Advanced" or "CPU Configuration"
echo [!] 2. Enable "Intel Virtualization Technology" or "Intel VT-x"
echo [!] 3. Find "Security" or "Boot" settings
echo [!] 4. Disable "Secure Boot"
echo [!] 5. Save and Exit (usually F10)
echo.
echo Press any key to cancel the restart...
pause >nul

REM Cancel the restart if user pressed a key
shutdown /a
echo [+] Restart cancelled.

echo.
echo [+] Alternative methods:
echo [!] Method 2: Manual restart to BIOS
echo [!] - Restart your computer
echo [!] - Press DEL or F2 repeatedly during boot
echo [!] - Look for ASUS BIOS setup
echo.
echo [!] Method 3: Windows Settings
echo [!] - Settings > Update & Security > Recovery
echo [!] - Advanced startup > Restart now
echo [!] - Troubleshoot > Advanced options > UEFI Firmware Settings
echo.
pause
