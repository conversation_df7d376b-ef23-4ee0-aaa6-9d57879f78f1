#!/usr/bin/env python3
"""
SecureToggle - Windows Security Protection Toggle Tool
Hardware-locked GUI application for toggling Windows security features.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import sys
import subprocess
import winreg
import wmi
import platform
import uuid
import ctypes
from pathlib import Path

class SecurityToggle:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("SecureToggle - Windows Security Manager")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # Load configuration and verify system lock
        self.config = self.load_config()
        if not self.verify_system_lock():
            messagebox.showerror("Access Denied", 
                               "This application is locked to a different system.\n"
                               "Hardware fingerprint mismatch detected.")
            sys.exit(1)
        
        # Check for admin privileges
        if not self.is_admin():
            messagebox.showerror("Administrator Required", 
                               "This application requires administrator privileges.\n"
                               "Please run as administrator.")
            sys.exit(1)
        
        # Initialize WMI
        try:
            self.wmi_conn = wmi.WMI()
        except Exception as e:
            messagebox.showerror("WMI Error", f"Failed to initialize WMI: {e}")
            sys.exit(1)
        
        self.setup_ui()
        self.refresh_status()
    
    def load_config(self):
        """Load the lock configuration file."""
        config_path = Path(__file__).parent / "secure_toggle_lock_config.json"
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            messagebox.showerror("Configuration Error", 
                               "Lock configuration file not found.")
            sys.exit(1)
        except json.JSONDecodeError:
            messagebox.showerror("Configuration Error", 
                               "Invalid lock configuration file.")
            sys.exit(1)
    
    def verify_system_lock(self):
        """Verify system fingerprint against lock configuration."""
        if not self.config.get("system_lock", {}).get("lock_enabled", False):
            return True
        
        lock_config = self.config["system_lock"]
        
        # Get current system info
        current_hostname = platform.node()
        current_uuid = str(uuid.getnode())
        
        # Check hostname
        if current_hostname != lock_config.get("hostname"):
            return False
        
        # Check UUID (MAC-based)
        if current_uuid != lock_config.get("uuid"):
            return False
        
        # Additional checks could be added here for BIOS serial, motherboard, etc.
        return True
    
    def is_admin(self):
        """Check if running with administrator privileges."""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def setup_ui(self):
        """Setup the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="SecureToggle", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # System info
        system_frame = ttk.LabelFrame(main_frame, text="System Information", padding="10")
        system_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))
        
        hostname_label = ttk.Label(system_frame, text=f"Hostname: {platform.node()}")
        hostname_label.grid(row=0, column=0, sticky=tk.W)
        
        os_label = ttk.Label(system_frame, text=f"OS: {platform.system()} {platform.release()}")
        os_label.grid(row=1, column=0, sticky=tk.W)
        
        # Security features frame
        security_frame = ttk.LabelFrame(main_frame, text="Security Features", padding="10")
        security_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # Headers
        ttk.Label(security_frame, text="Feature", font=("Arial", 10, "bold")).grid(row=0, column=0, padx=(0, 20))
        ttk.Label(security_frame, text="Status", font=("Arial", 10, "bold")).grid(row=0, column=1, padx=(0, 20))
        ttk.Label(security_frame, text="Action", font=("Arial", 10, "bold")).grid(row=0, column=2)
        
        # Windows Defender
        self.defender_status = tk.StringVar(value="Checking...")
        ttk.Label(security_frame, text="Windows Defender").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.defender_status_label = ttk.Label(security_frame, textvariable=self.defender_status)
        self.defender_status_label.grid(row=1, column=1, pady=5)
        self.defender_button = ttk.Button(security_frame, text="Toggle", 
                                        command=lambda: self.toggle_defender())
        self.defender_button.grid(row=1, column=2, pady=5)
        
        # VBS
        self.vbs_status = tk.StringVar(value="Checking...")
        ttk.Label(security_frame, text="Virtualization-based Security").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.vbs_status_label = ttk.Label(security_frame, textvariable=self.vbs_status)
        self.vbs_status_label.grid(row=2, column=1, pady=5)
        self.vbs_button = ttk.Button(security_frame, text="Toggle", 
                                   command=lambda: self.toggle_vbs())
        self.vbs_button.grid(row=2, column=2, pady=5)
        
        # HVCI
        self.hvci_status = tk.StringVar(value="Checking...")
        ttk.Label(security_frame, text="HVCI (Memory Integrity)").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.hvci_status_label = ttk.Label(security_frame, textvariable=self.hvci_status)
        self.hvci_status_label.grid(row=3, column=1, pady=5)
        self.hvci_button = ttk.Button(security_frame, text="Toggle", 
                                    command=lambda: self.toggle_hvci())
        self.hvci_button.grid(row=3, column=2, pady=5)
        
        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=20)
        
        refresh_button = ttk.Button(button_frame, text="Refresh Status", 
                                  command=self.refresh_status)
        refresh_button.grid(row=0, column=0, padx=(0, 10))
        
        exit_button = ttk.Button(button_frame, text="Exit", 
                               command=self.root.quit)
        exit_button.grid(row=0, column=1)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                             relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(20, 0))
    
    def update_status_color(self, label, status):
        """Update label color based on status."""
        if status == "Enabled":
            label.configure(foreground="green")
        elif status == "Disabled":
            label.configure(foreground="red")
        else:
            label.configure(foreground="orange")
    
    def check_defender_status(self):
        """Check Windows Defender status."""
        try:
            # Check via WMI
            antivirus_products = self.wmi_conn.query("SELECT * FROM AntiVirusProduct", namespace="root\\SecurityCenter2")
            for product in antivirus_products:
                if "Windows Defender" in product.displayName:
                    # Product state interpretation (simplified)
                    state = product.productState
                    if state & 0x1000:  # Real-time protection enabled
                        return "Enabled"
                    else:
                        return "Disabled"
            
            # Fallback: Check registry
            try:
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                   r"SOFTWARE\Policies\Microsoft\Windows Defender")
                value, _ = winreg.QueryValueEx(key, "DisableAntiSpyware")
                winreg.CloseKey(key)
                return "Disabled" if value == 1 else "Enabled"
            except FileNotFoundError:
                return "Enabled"  # Default if no policy set
            
        except Exception as e:
            return f"Error: {str(e)[:20]}..."
    
    def check_vbs_status(self):
        """Check VBS status."""
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                               r"SYSTEM\CurrentControlSet\Control\DeviceGuard")
            value, _ = winreg.QueryValueEx(key, "EnableVirtualizationBasedSecurity")
            winreg.CloseKey(key)
            return "Enabled" if value == 1 else "Disabled"
        except FileNotFoundError:
            return "Disabled"  # Default if not configured
        except Exception as e:
            return f"Error: {str(e)[:20]}..."
    
    def check_hvci_status(self):
        """Check HVCI status."""
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                               r"SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios\HypervisorEnforcedCodeIntegrity")
            value, _ = winreg.QueryValueEx(key, "Enabled")
            winreg.CloseKey(key)
            return "Enabled" if value == 1 else "Disabled"
        except FileNotFoundError:
            return "Disabled"  # Default if not configured
        except Exception as e:
            return f"Error: {str(e)[:20]}..."

    def refresh_status(self):
        """Refresh all security feature statuses."""
        self.status_var.set("Refreshing status...")
        self.root.update()

        # Check Defender
        defender_status = self.check_defender_status()
        self.defender_status.set(defender_status)
        self.update_status_color(self.defender_status_label, defender_status)

        # Check VBS
        vbs_status = self.check_vbs_status()
        self.vbs_status.set(vbs_status)
        self.update_status_color(self.vbs_status_label, vbs_status)

        # Check HVCI
        hvci_status = self.check_hvci_status()
        self.hvci_status.set(hvci_status)
        self.update_status_color(self.hvci_status_label, hvci_status)

        self.status_var.set("Status updated")

    def run_registry_command(self, command):
        """Run a registry command with error handling."""
        try:
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                return True, "Success"
            else:
                return False, result.stderr
        except Exception as e:
            return False, str(e)

    def toggle_defender(self):
        """Toggle Windows Defender."""
        current_status = self.defender_status.get()

        if current_status == "Enabled":
            # Disable Defender
            commands = [
                'reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender" /v "DisableAntiSpyware" /t REG_DWORD /d 1 /f',
                'reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Real-Time Protection" /v "DisableRealtimeMonitoring" /t REG_DWORD /d 1 /f'
            ]
            action = "Disabling"
        else:
            # Enable Defender
            commands = [
                'reg delete "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender" /v "DisableAntiSpyware" /f',
                'reg delete "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Real-Time Protection" /v "DisableRealtimeMonitoring" /f'
            ]
            action = "Enabling"

        self.status_var.set(f"{action} Windows Defender...")
        self.root.update()

        success = True
        for command in commands:
            result, message = self.run_registry_command(command)
            if not result:
                success = False
                break

        if success:
            self.status_var.set(f"Windows Defender {action.lower()} completed")
            messagebox.showinfo("Success", f"Windows Defender {action.lower()} completed.\nRestart may be required for full effect.")
        else:
            self.status_var.set("Operation failed")
            messagebox.showerror("Error", f"Failed to toggle Windows Defender: {message}")

        self.refresh_status()

    def toggle_vbs(self):
        """Toggle Virtualization-based Security."""
        current_status = self.vbs_status.get()

        if current_status == "Enabled":
            # Disable VBS
            command = 'reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\DeviceGuard" /v "EnableVirtualizationBasedSecurity" /t REG_DWORD /d 0 /f'
            action = "Disabling"
        else:
            # Enable VBS
            command = 'reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\DeviceGuard" /v "EnableVirtualizationBasedSecurity" /t REG_DWORD /d 1 /f'
            action = "Enabling"

        self.status_var.set(f"{action} VBS...")
        self.root.update()

        result, message = self.run_registry_command(command)

        if result:
            self.status_var.set(f"VBS {action.lower()} completed")
            messagebox.showinfo("Success", f"VBS {action.lower()} completed.\nRestart required for changes to take effect.")
        else:
            self.status_var.set("Operation failed")
            messagebox.showerror("Error", f"Failed to toggle VBS: {message}")

        self.refresh_status()

    def toggle_hvci(self):
        """Toggle HVCI (Memory Integrity)."""
        current_status = self.hvci_status.get()

        if current_status == "Enabled":
            # Disable HVCI
            command = 'reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\DeviceGuard\\Scenarios\\HypervisorEnforcedCodeIntegrity" /v "Enabled" /t REG_DWORD /d 0 /f'
            action = "Disabling"
        else:
            # Enable HVCI
            command = 'reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\DeviceGuard\\Scenarios\\HypervisorEnforcedCodeIntegrity" /v "Enabled" /t REG_DWORD /d 1 /f'
            action = "Enabling"

        self.status_var.set(f"{action} HVCI...")
        self.root.update()

        result, message = self.run_registry_command(command)

        if result:
            self.status_var.set(f"HVCI {action.lower()} completed")
            messagebox.showinfo("Success", f"HVCI {action.lower()} completed.\nRestart required for changes to take effect.")
        else:
            self.status_var.set("Operation failed")
            messagebox.showerror("Error", f"Failed to toggle HVCI: {message}")

        self.refresh_status()

    def run(self):
        """Start the GUI application."""
        self.root.mainloop()


def main():
    """Main entry point."""
    try:
        app = SecurityToggle()
        app.run()
    except KeyboardInterrupt:
        pass
    except Exception as e:
        messagebox.showerror("Fatal Error", f"An unexpected error occurred: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
