
import tkinter as tk
from tkinter import messagebox
import os
import platform
import socket
import uuid
import wmi
import json

# Load lock config
config_file = "secure_toggle_lock_config.json"
try:
    with open(config_file, "r") as f:
        config = json.load(f)
except Exception:
    messagebox.showerror("ERROR", "Missing secure_toggle_lock_config.json file.")
    exit()

def check_identity():
    try:
        c = wmi.WMI()
        bios = c.Win32_BIOS()[0]
        board = c.Win32_BaseBoard()[0]

        match = (
            socket.gethostname() == config["hostname"] and
            str(uuid.getnode()) == config["UUID"] and
            bios.SerialNumber.strip() == config["BIOSSerial"] and
            board.Manufacturer.strip() == config["BoardManufacturer"] and
            board.Product.strip() == config["BoardProduct"]
        )
        return match
    except Exception:
        return False

if not check_identity():
    messagebox.showerror("Unauthorized", "This app is locked to a specific machine.")
    exit()

def get_status():
    try:
        import winreg

        # VBS
        vbs_key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Control\DeviceGuard")
        vbs_status, _ = winreg.QueryValueEx(vbs_key, "EnableVirtualizationBasedSecurity")
        winreg.CloseKey(vbs_key)

        # HVCI
        hvci_key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios\HypervisorEnforcedCodeIntegrity")
        hvci_status, _ = winreg.QueryValueEx(hvci_key, "Enabled")
        winreg.CloseKey(hvci_key)

        # Defender
        try:
            def_key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Policies\Microsoft\Windows Defender")
            defender_status, _ = winreg.QueryValueEx(def_key, "DisableAntiSpyware")
            winreg.CloseKey(def_key)
        except FileNotFoundError:
            defender_status = 0

        if vbs_status == 0 and hvci_status == 0 and defender_status == 1:
            return "DISABLED"
        else:
            return "ENABLED"
    except Exception:
        return "ERROR"

def toggle_protection(mode):
    if mode == "disable":
        os.system("reg add \"HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard\" /v EnableVirtualizationBasedSecurity /t REG_DWORD /d 0 /f")
        os.system("reg add \"HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios\HypervisorEnforcedCodeIntegrity\" /v Enabled /t REG_DWORD /d 0 /f")
        os.system("reg add \"HKLM\SOFTWARE\Policies\Microsoft\Windows Defender\" /v DisableAntiSpyware /t REG_DWORD /d 1 /f")
        os.system("reg add \"HKLM\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection\" /v DisableRealtimeMonitoring /t REG_DWORD /d 1 /f")
    else:
        os.system("reg add \"HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard\" /v EnableVirtualizationBasedSecurity /t REG_DWORD /d 1 /f")
        os.system("reg add \"HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios\HypervisorEnforcedCodeIntegrity\" /v Enabled /t REG_DWORD /d 1 /f")
        os.system("reg delete \"HKLM\SOFTWARE\Policies\Microsoft\Windows Defender\" /v DisableAntiSpyware /f")
        os.system("reg delete \"HKLM\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection\" /v DisableRealtimeMonitoring /f")
    update_status()

def update_status():
    status = get_status()
    label_var.set(f"Protection Status: {status}")
    if status == "DISABLED":
        label.config(fg="green")
    elif status == "ENABLED":
        label.config(fg="red")
    else:
        label.config(fg="orange")

# GUI setup
app = tk.Tk()
app.title("SecureToggle")
app.geometry("400x200")

label_var = tk.StringVar()
label = tk.Label(app, textvariable=label_var, font=("Segoe UI", 14))
label.pack(pady=20)

btn_frame = tk.Frame(app)
btn_frame.pack(pady=10)

disable_btn = tk.Button(btn_frame, text="Disable Protections", command=lambda: toggle_protection("disable"), bg="black", fg="white")
disable_btn.grid(row=0, column=0, padx=10)

enable_btn = tk.Button(btn_frame, text="Enable Protections", command=lambda: toggle_protection("enable"), bg="gray", fg="white")
enable_btn.grid(row=0, column=1, padx=10)

update_status()
app.mainloop()
