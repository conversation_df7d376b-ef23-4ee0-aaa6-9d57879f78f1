[5578:5FC8][2025-03-02T01:34:12]i001: Burn v3.7.3424.0, Windows v10.0 (Build 19045: Service Pack 0), path: C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp13\cp13b.exe, cmdline: '/quiet /repair /norestart /log "C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log13b.txt" -burn.unelevated BurnPipe.{739358F5-9CDA-4340-A012-034A1EB76F7C} {CDE2F023-8E7F-458E-8AA8-A36D4BFE8E8A} 24800'
[5578:5FC8][2025-03-02T01:34:12]i000: Setting string variable 'WixBundleLog' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log13b.txt'
[5578:5FC8][2025-03-02T01:34:12]i000: Setting string variable 'WixBundleOriginalSource' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp13\cp13b.exe'
[5578:5FC8][2025-03-02T01:34:12]i000: Setting string variable 'WixBundleOriginalSourceFolder' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp13\'
[5578:5FC8][2025-03-02T01:34:13]i000: Setting string variable 'WixBundleName' to value 'Microsoft Visual C++ 2013 Redistributable (x86) - 12.0.40664'
[5578:5FC8][2025-03-02T01:34:13]i100: Detect begin, 2 packages
[5578:5FC8][2025-03-02T01:34:13]i103: Detected related package: {13A4EE12-23EA-3371-91EE-EFB36DDFFF3E}, scope: PerMachine, version: 12.0.21005.0, language: 0 operation: MajorUpgrade
[5578:5FC8][2025-03-02T01:34:13]i103: Detected related package: {F8CFEB22-A2E7-3971-9EDA-4B11EDEFC185}, scope: PerMachine, version: 12.0.21005.0, language: 0 operation: MajorUpgrade
[5578:5FC8][2025-03-02T01:34:13]i101: Detected package: vcRuntimeMinimum_x86, state: Absent, cached: None
[5578:5FC8][2025-03-02T01:34:13]i101: Detected package: vcRuntimeAdditional_x86, state: Absent, cached: None
[5578:5FC8][2025-03-02T01:34:13]i052: Condition 'VersionNT >= v6.0 OR (VersionNT = v5.1 AND ServicePackLevel >= 2) OR (VersionNT = v5.2 AND ServicePackLevel >= 1)' evaluates to true.
[5578:5FC8][2025-03-02T01:34:13]i199: Detect complete, result: 0x0
[5578:5FC8][2025-03-02T01:34:13]i200: Plan begin, 2 packages, action: Install
[5578:5FC8][2025-03-02T01:34:13]i000: Setting string variable 'WixBundleRollbackLog_vcRuntimeMinimum_x86' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log13b_000_vcRuntimeMinimum_x86_rollback.txt'
[5578:5FC8][2025-03-02T01:34:13]i000: Setting string variable 'WixBundleLog_vcRuntimeMinimum_x86' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log13b_000_vcRuntimeMinimum_x86.txt'
[5578:5FC8][2025-03-02T01:34:13]i000: Setting string variable 'WixBundleRollbackLog_vcRuntimeAdditional_x86' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log13b_001_vcRuntimeAdditional_x86_rollback.txt'
[5578:5FC8][2025-03-02T01:34:13]i000: Setting string variable 'WixBundleLog_vcRuntimeAdditional_x86' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log13b_001_vcRuntimeAdditional_x86.txt'
[5578:5FC8][2025-03-02T01:34:13]i201: Planned package: vcRuntimeMinimum_x86, state: Absent, default requested: Present, ba requested: Present, execute: Install, rollback: Uninstall, cache: Yes, uncache: No, dependency: Register
[5578:5FC8][2025-03-02T01:34:13]i201: Planned package: vcRuntimeAdditional_x86, state: Absent, default requested: Present, ba requested: Present, execute: Install, rollback: Uninstall, cache: Yes, uncache: No, dependency: Register
[5578:5FC8][2025-03-02T01:34:13]i299: Plan complete, result: 0x0
[5578:5FC8][2025-03-02T01:34:13]i300: Apply begin
[60E0:5B58][2025-03-02T01:34:13]i360: Creating a system restore point.
[60E0:5B58][2025-03-02T01:34:13]i361: Created a system restore point.
[60E0:5B58][2025-03-02T01:34:13]i370: Session begin, registration key: SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{9dff3540-fc85-4ed5-ac84-9e3c7fd8bece}, options: 0x7, disable resume: No
[60E0:5B58][2025-03-02T01:34:13]i000: Caching bundle from: 'C:\Users\<USER>\AppData\Local\Temp\{9dff3540-fc85-4ed5-ac84-9e3c7fd8bece}\.be\vcredist_x86.exe' to: 'C:\ProgramData\Package Cache\{9dff3540-fc85-4ed5-ac84-9e3c7fd8bece}\vcredist_x86.exe'
[60E0:5B58][2025-03-02T01:34:13]i320: Registering bundle dependency provider: {9dff3540-fc85-4ed5-ac84-9e3c7fd8bece}, version: 12.0.40664.0
[60E0:5B58][2025-03-02T01:34:13]i371: Updating session, registration key: SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{9dff3540-fc85-4ed5-ac84-9e3c7fd8bece}, resume: Active, restart initiated: No, disable resume: No
[60E0:886C][2025-03-02T01:34:13]i305: Verified acquired payload: vcRuntimeMinimum_x86 at path: C:\ProgramData\Package Cache\.unverified\vcRuntimeMinimum_x86, moving to: C:\ProgramData\Package Cache\{8122DAB1-ED4D-3676-BB0A-CA368196543E}v12.0.40664\packages\vcRuntimeMinimum_x86\vc_runtimeMinimum_x86.msi.
[60E0:886C][2025-03-02T01:34:13]i305: Verified acquired payload: cab54A5CABBE7274D8A22EB58060AAB7623 at path: C:\ProgramData\Package Cache\.unverified\cab54A5CABBE7274D8A22EB58060AAB7623, moving to: C:\ProgramData\Package Cache\{8122DAB1-ED4D-3676-BB0A-CA368196543E}v12.0.40664\packages\vcRuntimeMinimum_x86\cab1.cab.
[60E0:886C][2025-03-02T01:34:13]i305: Verified acquired payload: vcRuntimeAdditional_x86 at path: C:\ProgramData\Package Cache\.unverified\vcRuntimeAdditional_x86, moving to: C:\ProgramData\Package Cache\{D401961D-3A20-3AC7-943B-6139D5BD490A}v12.0.40664\packages\vcRuntimeAdditional_x86\vc_runtimeAdditional_x86.msi.
[60E0:886C][2025-03-02T01:34:13]i305: Verified acquired payload: cabB3E1576D1FEFBB979E13B1A5379E0B16 at path: C:\ProgramData\Package Cache\.unverified\cabB3E1576D1FEFBB979E13B1A5379E0B16, moving to: C:\ProgramData\Package Cache\{D401961D-3A20-3AC7-943B-6139D5BD490A}v12.0.40664\packages\vcRuntimeAdditional_x86\cab1.cab.
[60E0:5B58][2025-03-02T01:34:13]i301: Applying execute package: vcRuntimeMinimum_x86, action: Install, path: C:\ProgramData\Package Cache\{8122DAB1-ED4D-3676-BB0A-CA368196543E}v12.0.40664\packages\vcRuntimeMinimum_x86\vc_runtimeMinimum_x86.msi, arguments: ' MSIFASTINSTALL="7" NOVSUI="1"'
[5578:5FC8][2025-03-02T01:34:13]i319: Applied execute package: vcRuntimeMinimum_x86, result: 0x0, restart: None
[60E0:5B58][2025-03-02T01:34:13]i325: Registering dependency: {9dff3540-fc85-4ed5-ac84-9e3c7fd8bece} on package provider: Microsoft.VS.VC_RuntimeMinimumVSU_x86,v12, package: vcRuntimeMinimum_x86
[60E0:5B58][2025-03-02T01:34:13]i301: Applying execute package: vcRuntimeAdditional_x86, action: Install, path: C:\ProgramData\Package Cache\{D401961D-3A20-3AC7-943B-6139D5BD490A}v12.0.40664\packages\vcRuntimeAdditional_x86\vc_runtimeAdditional_x86.msi, arguments: ' MSIFASTINSTALL="7" NOVSUI="1"'
[5578:5FC8][2025-03-02T01:34:13]i319: Applied execute package: vcRuntimeAdditional_x86, result: 0x0, restart: None
[60E0:5B58][2025-03-02T01:34:13]i325: Registering dependency: {9dff3540-fc85-4ed5-ac84-9e3c7fd8bece} on package provider: Microsoft.VS.VC_RuntimeAdditionalVSU_x86,v12, package: vcRuntimeAdditional_x86
[60E0:5B58][2025-03-02T01:34:13]i372: Session end, registration key: SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{9dff3540-fc85-4ed5-ac84-9e3c7fd8bece}, resume: ARP, restart: None, disable resume: No
[60E0:5B58][2025-03-02T01:34:13]i371: Updating session, registration key: SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{9dff3540-fc85-4ed5-ac84-9e3c7fd8bece}, resume: ARP, restart initiated: No, disable resume: No
[5578:5FC8][2025-03-02T01:34:13]i399: Apply complete, result: 0x0, restart: None, ba requested restart:  No
[5578:5FC8][2025-03-02T01:34:13]i500: Shutting down, exit code: 0x0
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: VersionNT = 10.0.0.0
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: WixBundleAction = 5
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: WixBundleElevated = 1
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: WixBundleInstalled = 0
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: WixBundleLog = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log13b.txt
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: WixBundleLog_vcRuntimeAdditional_x86 = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log13b_001_vcRuntimeAdditional_x86.txt
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: WixBundleLog_vcRuntimeMinimum_x86 = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log13b_000_vcRuntimeMinimum_x86.txt
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: WixBundleManufacturer = Microsoft Corporation
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: WixBundleName = Microsoft Visual C++ 2013 Redistributable (x86) - 12.0.40664
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: WixBundleOriginalSource = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp13\cp13b.exe
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: WixBundleOriginalSourceFolder = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp13\
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: WixBundleProviderKey = {9dff3540-fc85-4ed5-ac84-9e3c7fd8bece}
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: WixBundleRollbackLog_vcRuntimeAdditional_x86 = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log13b_001_vcRuntimeAdditional_x86_rollback.txt
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: WixBundleRollbackLog_vcRuntimeMinimum_x86 = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log13b_000_vcRuntimeMinimum_x86_rollback.txt
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: WixBundleTag = 
[5578:5FC8][2025-03-02T01:34:13]i410: Variable: WixBundleVersion = 12.0.40664.0
[5578:5FC8][2025-03-02T01:34:13]i007: Exit code: 0x0, restarting: No
