@echo off
echo ===================================================
echo SecureToggle Comprehensive - Admin Launcher
echo ===================================================

REM Check for Administrator privileges
NET SESSION >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Requesting Administrator privileges...
    echo.
    echo Please click "Yes" when prompted by UAC.
    echo.
    
    REM Request admin privileges and re-run this script
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

echo Running with Administrator privileges...
echo.

REM Check if Python is available
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Python is not installed or not in PATH.
    echo Please install Python and try again.
    pause
    exit /b 1
)

echo Starting SecureToggle Comprehensive...
echo.

REM Change to the script directory
cd /d "%~dp0"

REM Run the comprehensive SecureToggle application
python secure_toggle_comprehensive.pyw

if %ERRORLEVEL% neq 0 (
    echo.
    echo Application exited with error code: %ERRORLEVEL%
    pause
)

echo.
echo SecureToggle Comprehensive closed.
pause
