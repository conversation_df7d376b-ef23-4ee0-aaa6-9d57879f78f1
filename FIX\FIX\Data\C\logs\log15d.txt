[78C4:1998][2025-03-02T01:34:21]i001: Burn v3.10.4.4718, Windows v10.0 (Build 19045: Service Pack 0), path: C:\Windows\Temp\{41C1234A-9942-4DE9-A600-D322A3561B15}\.cr\cp15d.exe
[78C4:1998][2025-03-02T01:34:21]i009: Command Line: '"-burn.clean.room=C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp15\cp15d.exe" -burn.filehandle.attached=584 -burn.filehandle.self=580 /quiet /repair /norestart /log "C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log15d.txt"'
[78C4:1998][2025-03-02T01:34:21]i000: Setting string variable 'WixBundleOriginalSource' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp15\cp15d.exe'
[78C4:1998][2025-03-02T01:34:21]i000: Setting string variable 'WixBundleOriginalSourceFolder' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp15\'
[78C4:1998][2025-03-02T01:34:21]i000: Setting string variable 'WixBundleLog' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log15d.txt'
[78C4:1998][2025-03-02T01:34:21]i000: Setting string variable 'WixBundleName' to value 'Microsoft Visual C++ 2015-2019 Redistributable (x86) - 14.25.28508'
[78C4:1998][2025-03-02T01:34:21]i000: Setting string variable 'WixBundleManufacturer' to value 'Microsoft Corporation'
[78C4:51D8][2025-03-02T01:34:21]i000: Setting version variable 'WixBundleFileVersion' to value '14.25.28508.3'
[78C4:1998][2025-03-02T01:34:21]i100: Detect begin, 10 packages
[78C4:1998][2025-03-02T01:34:21]i000: Setting version variable 'windows_uCRT_DetectKey' to value '10.0.19041.3636'
[78C4:1998][2025-03-02T01:34:21]i000: Setting numeric variable 'windows_uCRT_DetectKeyExists' to value 1
[78C4:1998][2025-03-02T01:34:21]i102: Detected related bundle: {e7802eac-3305-4da0-9378-e55d1ed05518}, type: Upgrade, scope: PerMachine, version: 14.42.34433.0, operation: Downgrade
[78C4:1998][2025-03-02T01:34:21]i052: Condition '(VersionNT = v6.3 AND NOT VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[78C4:1998][2025-03-02T01:34:21]i052: Condition '(VersionNT = v6.3 AND VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[78C4:1998][2025-03-02T01:34:21]i052: Condition '(VersionNT = v6.2 AND NOT VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[78C4:1998][2025-03-02T01:34:21]i052: Condition '(VersionNT = v6.2 AND VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[78C4:1998][2025-03-02T01:34:21]i052: Condition '(VersionNT = v6.1 AND NOT VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[78C4:1998][2025-03-02T01:34:21]i052: Condition '(VersionNT = v6.1 AND VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[78C4:1998][2025-03-02T01:34:21]i052: Condition '(VersionNT = v6.0 AND NOT VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[78C4:1998][2025-03-02T01:34:21]i052: Condition '(VersionNT = v6.0 AND VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[78C4:1998][2025-03-02T01:34:21]i103: Detected related package: {C2BB95AA-90F3-4891-81C1-A7E565BB836C}, scope: PerMachine, version: 14.42.34433.0, language: 0 operation: Downgrade
[78C4:1998][2025-03-02T01:34:21]i103: Detected related package: {84E3E712-6343-484B-8B6C-9F145F019A70}, scope: PerMachine, version: 14.42.34433.0, language: 0 operation: Downgrade
[78C4:1998][2025-03-02T01:34:21]i101: Detected package: Windows81_x86, state: Absent, cached: None
[78C4:1998][2025-03-02T01:34:21]i101: Detected package: Windows81_x64, state: Absent, cached: None
[78C4:1998][2025-03-02T01:34:21]i101: Detected package: Windows8_x86, state: Absent, cached: None
[78C4:1998][2025-03-02T01:34:21]i101: Detected package: Windows8_x64, state: Absent, cached: None
[78C4:1998][2025-03-02T01:34:21]i101: Detected package: Windows7_MSU_x86, state: Absent, cached: None
[78C4:1998][2025-03-02T01:34:21]i101: Detected package: Windows7_MSU_x64, state: Absent, cached: None
[78C4:1998][2025-03-02T01:34:21]i101: Detected package: WindowsVista_MSU_x86, state: Absent, cached: None
[78C4:1998][2025-03-02T01:34:21]i101: Detected package: WindowsVista_MSU_x64, state: Absent, cached: None
[78C4:1998][2025-03-02T01:34:21]i101: Detected package: vcRuntimeMinimum_x86, state: Obsolete, cached: None
[78C4:1998][2025-03-02T01:34:21]i101: Detected package: vcRuntimeAdditional_x86, state: Obsolete, cached: None
[78C4:1998][2025-03-02T01:34:21]i052: Condition 'VersionNT >= v6.0 OR (VersionNT = v5.1 AND ServicePackLevel >= 2) OR (VersionNT = v5.2 AND ServicePackLevel >= 1)' evaluates to true.
[78C4:1998][2025-03-02T01:34:21]i199: Detect complete, result: 0x0
[78C4:51D8][2025-03-02T01:34:21]e000: Error 0x80070666: Cannot install a product when a newer version is installed.
[78C4:1998][2025-03-02T01:34:21]i500: Shutting down, exit code: 0x666
[78C4:1998][2025-03-02T01:34:21]i410: Variable: SystemFolder = C:\Windows\system32\
[78C4:1998][2025-03-02T01:34:21]i410: Variable: VersionNT = 10.0.0.0
[78C4:1998][2025-03-02T01:34:21]i410: Variable: VersionNT64 = 10.0.0.0
[78C4:1998][2025-03-02T01:34:21]i410: Variable: windows_uCRT_DetectKey = 10.0.19041.3636
[78C4:1998][2025-03-02T01:34:21]i410: Variable: windows_uCRT_DetectKeyExists = 1
[78C4:1998][2025-03-02T01:34:21]i410: Variable: WixBundleAction = 7
[78C4:1998][2025-03-02T01:34:21]i410: Variable: WixBundleElevated = 1
[78C4:1998][2025-03-02T01:34:21]i410: Variable: WixBundleFileVersion = 14.25.28508.3
[78C4:1998][2025-03-02T01:34:21]i410: Variable: WixBundleInstalled = 0
[78C4:1998][2025-03-02T01:34:21]i410: Variable: WixBundleLog = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log15d.txt
[78C4:1998][2025-03-02T01:34:21]i410: Variable: WixBundleManufacturer = Microsoft Corporation
[78C4:1998][2025-03-02T01:34:21]i410: Variable: WixBundleName = Microsoft Visual C++ 2015-2019 Redistributable (x86) - 14.25.28508
[78C4:1998][2025-03-02T01:34:21]i410: Variable: WixBundleOriginalSource = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp15\cp15d.exe
[78C4:1998][2025-03-02T01:34:21]i410: Variable: WixBundleOriginalSourceFolder = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp15\
[78C4:1998][2025-03-02T01:34:21]i410: Variable: WixBundleProviderKey = VC,redist.x86,x86,14.25,bundle
[78C4:1998][2025-03-02T01:34:21]i410: Variable: WixBundleSourceProcessFolder = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp15\
[78C4:1998][2025-03-02T01:34:21]i410: Variable: WixBundleSourceProcessPath = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp15\cp15d.exe
[78C4:1998][2025-03-02T01:34:21]i410: Variable: WixBundleTag = 
[78C4:1998][2025-03-02T01:34:21]i410: Variable: WixBundleVersion = 14.25.28508.3
[78C4:1998][2025-03-02T01:34:21]i007: Exit code: 0x666, restarting: No
