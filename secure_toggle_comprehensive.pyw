#!/usr/bin/env python3
"""
SecureToggle Comprehensive - Complete Progress Software Setup Tool
Hardware-locked GUI application for comprehensive Windows security and optimization.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import os
import sys
import subprocess
import winreg
import platform
import uuid
import ctypes
import threading
import time
import zipfile
import shutil
from pathlib import Path

# Try to import WMI, but handle gracefully if not available
try:
    import wmi
    WMI_AVAILABLE = True
except ImportError:
    WMI_AVAILABLE = False
    print("Warning: WMI module not available. Some features may be limited.")

class ComprehensiveSecurityToggle:
    def __init__(self):
        print("Starting SecureToggle Comprehensive...")
        
        self.root = tk.Tk()
        self.root.title("SecureToggle Comprehensive - Progress Setup Tool")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        print("Loading configuration...")
        # Load configuration and verify system lock
        self.config = self.load_config()
        
        print("Verifying system lock...")
        if not self.verify_system_lock():
            print("System lock verification failed!")
            messagebox.showerror("Access Denied", 
                               "This application is locked to a different system.\n"
                               "Hardware fingerprint mismatch detected.")
            sys.exit(1)
        
        print("Checking admin privileges...")
        # Check for admin privileges
        if not self.is_admin():
            print("Admin privileges required!")
            messagebox.showerror("Administrator Required", 
                               "This application requires administrator privileges.\n"
                               "Please run as administrator.")
            sys.exit(1)
        
        # Initialize WMI if available
        self.wmi_conn = None
        if WMI_AVAILABLE:
            try:
                self.wmi_conn = wmi.WMI()
            except Exception as e:
                print(f"Warning: Failed to initialize WMI: {e}")
                self.wmi_conn = None
        
        self.setup_ui()
        self.refresh_all_status()

        # Initialize Auth_PG status
        self.check_auth_pg_status()
    
    def load_config(self):
        """Load the lock configuration file."""
        config_path = Path(__file__).parent / "secure_toggle_lock_config.json"
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            messagebox.showerror("Configuration Error", 
                               "Lock configuration file not found.")
            sys.exit(1)
        except json.JSONDecodeError:
            messagebox.showerror("Configuration Error", 
                               "Invalid lock configuration file.")
            sys.exit(1)
    
    def verify_system_lock(self):
        """Verify system fingerprint against lock configuration."""
        if not self.config.get("system_lock", {}).get("lock_enabled", False):
            return True
        
        lock_config = self.config["system_lock"]
        
        # Get current system info
        current_hostname = platform.node()
        current_uuid = str(uuid.getnode())
        
        # Check hostname
        if current_hostname != lock_config.get("hostname"):
            return False
        
        # Check UUID (MAC-based)
        if current_uuid != lock_config.get("uuid"):
            return False
        
        return True
    
    def is_admin(self):
        """Check if running with administrator privileges."""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def setup_ui(self):
        """Setup the comprehensive user interface."""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Tab 1: Security Features
        self.setup_security_tab(notebook)
        
        # Tab 2: System Optimization
        self.setup_optimization_tab(notebook)
        
        # Tab 3: Progress Environment
        self.setup_progress_tab(notebook)
        
        # Tab 4: Installation Tools
        self.setup_installation_tab(notebook)

        # Tab 5: Auth_PG Integration
        self.setup_auth_pg_tab(notebook)

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var,
                             relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=(0, 10))
    
    def setup_security_tab(self, notebook):
        """Setup the security features tab."""
        security_frame = ttk.Frame(notebook)
        notebook.add(security_frame, text="Security Features")
        
        # System info
        info_frame = ttk.LabelFrame(security_frame, text="System Information", padding="10")
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(info_frame, text=f"Hostname: {platform.node()}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"OS: {platform.system()} {platform.release()}").pack(anchor=tk.W)
        
        # Security features
        sec_frame = ttk.LabelFrame(security_frame, text="Security Controls", padding="10")
        sec_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Headers
        ttk.Label(sec_frame, text="Feature", font=("Arial", 10, "bold")).grid(row=0, column=0, padx=(0, 20), sticky=tk.W)
        ttk.Label(sec_frame, text="Status", font=("Arial", 10, "bold")).grid(row=0, column=1, padx=(0, 20))
        ttk.Label(sec_frame, text="Action", font=("Arial", 10, "bold")).grid(row=0, column=2)
        
        # Security features
        self.security_features = {}
        features = [
            ("defender", "Windows Defender"),
            ("vbs", "Virtualization-based Security"),
            ("hvci", "HVCI (Memory Integrity)"),
            ("windows_update", "Windows Update"),
            ("firewall", "Windows Firewall"),
            ("uac", "User Account Control")
        ]
        
        for i, (key, name) in enumerate(features, 1):
            status_var = tk.StringVar(value="Checking...")
            status_label = ttk.Label(sec_frame, textvariable=status_var)
            button = ttk.Button(sec_frame, text="Toggle", 
                              command=lambda k=key: self.toggle_security_feature(k))
            
            ttk.Label(sec_frame, text=name).grid(row=i, column=0, sticky=tk.W, pady=5)
            status_label.grid(row=i, column=1, pady=5)
            button.grid(row=i, column=2, pady=5)
            
            self.security_features[key] = {
                'status_var': status_var,
                'status_label': status_label,
                'button': button
            }
        
        # Control buttons
        button_frame = ttk.Frame(sec_frame)
        button_frame.grid(row=len(features)+1, column=0, columnspan=3, pady=20)
        
        ttk.Button(button_frame, text="Refresh All", 
                  command=self.refresh_all_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Disable All Security", 
                  command=self.disable_all_security).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Enable All Security", 
                  command=self.enable_all_security).pack(side=tk.LEFT, padx=5)
    
    def setup_optimization_tab(self, notebook):
        """Setup the system optimization tab."""
        opt_frame = ttk.Frame(notebook)
        notebook.add(opt_frame, text="System Optimization")
        
        # Services optimization
        services_frame = ttk.LabelFrame(opt_frame, text="Windows Services", padding="10")
        services_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(services_frame, text="Disable Unnecessary Services", 
                  command=self.optimize_services).pack(side=tk.LEFT, padx=5)
        ttk.Button(services_frame, text="Restore Default Services", 
                  command=self.restore_services).pack(side=tk.LEFT, padx=5)
        
        # Performance optimization
        perf_frame = ttk.LabelFrame(opt_frame, text="Performance Settings", padding="10")
        perf_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(perf_frame, text="Apply Performance Tweaks", 
                  command=self.apply_performance_tweaks).pack(side=tk.LEFT, padx=5)
        ttk.Button(perf_frame, text="Set High Performance Power Plan", 
                  command=self.set_high_performance).pack(side=tk.LEFT, padx=5)
        
        # Game optimization
        game_frame = ttk.LabelFrame(opt_frame, text="Gaming Optimizations", padding="10")
        game_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(game_frame, text="Disable Game DVR", 
                  command=self.disable_game_dvr).pack(side=tk.LEFT, padx=5)
        ttk.Button(game_frame, text="Disable Fullscreen Optimizations", 
                  command=self.disable_fullscreen_opt).pack(side=tk.LEFT, padx=5)
        
        # Output log
        log_frame = ttk.LabelFrame(opt_frame, text="Operation Log", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=70)
        self.log_text.pack(fill=tk.BOTH, expand=True)
    
    def setup_progress_tab(self, notebook):
        """Setup the Progress environment tab."""
        progress_frame = ttk.Frame(notebook)
        notebook.add(progress_frame, text="Progress Environment")
        
        # Environment setup
        env_frame = ttk.LabelFrame(progress_frame, text="Progress Environment Setup", padding="10")
        env_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(env_frame, text="Create Progress Directories", 
                  command=self.create_progress_dirs).pack(side=tk.LEFT, padx=5)
        ttk.Button(env_frame, text="Set Environment Variables", 
                  command=self.set_progress_env_vars).pack(side=tk.LEFT, padx=5)
        ttk.Button(env_frame, text="Create Registry Entries", 
                  command=self.create_progress_registry).pack(side=tk.LEFT, padx=5)
        
        # Full setup
        full_frame = ttk.LabelFrame(progress_frame, text="Complete Setup", padding="10")
        full_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(full_frame, text="Run Complete Progress Setup", 
                  command=self.run_complete_progress_setup, 
                  style="Accent.TButton").pack(pady=10)
        
        # Progress info
        info_frame = ttk.LabelFrame(progress_frame, text="Progress Information", padding="10")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        info_text = tk.Text(info_frame, height=15, wrap=tk.WORD)
        info_text.pack(fill=tk.BOTH, expand=True)
        
        info_content = """Progress Software Environment Setup:

1. Creates necessary directories:
   - C:\\Progress\\OpenEdge
   - C:\\Progress\\OpenEdge\\bin
   - C:\\Progress\\Injection
   - C:\\Progress\\Tools

2. Sets environment variables:
   - PROGRESS_DLC = C:\\Progress\\OpenEdge
   - Updates PATH with Progress directories

3. Creates registry entries for Progress OpenEdge

4. Generates configuration files

This setup prepares your system for Progress software injection and development."""
        
        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)
    
    def setup_installation_tab(self, notebook):
        """Setup the installation tools tab."""
        install_frame = ttk.Frame(notebook)
        notebook.add(install_frame, text="Installation Tools")
        
        # Dependencies
        deps_frame = ttk.LabelFrame(install_frame, text="System Dependencies", padding="10")
        deps_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(deps_frame, text="Install DirectX", 
                  command=self.install_directx).pack(side=tk.LEFT, padx=5)
        ttk.Button(deps_frame, text="Install Visual C++ Redistributables", 
                  command=self.install_vcredist).pack(side=tk.LEFT, padx=5)
        
        # System tools
        tools_frame = ttk.LabelFrame(install_frame, text="System Tools", padding="10")
        tools_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(tools_frame, text="Clean Temporary Files", 
                  command=self.clean_temp_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(tools_frame, text="System File Check", 
                  command=self.run_sfc_scan).pack(side=tk.LEFT, padx=5)
        
        # Installation log
        install_log_frame = ttk.LabelFrame(install_frame, text="Installation Log", padding="10")
        install_log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.install_log_text = scrolledtext.ScrolledText(install_log_frame, height=15, width=70)
        self.install_log_text.pack(fill=tk.BOTH, expand=True)

    def setup_auth_pg_tab(self, notebook):
        """Setup the Auth_PG integration tab."""
        auth_pg_frame = ttk.Frame(notebook)
        notebook.add(auth_pg_frame, text="Auth_PG Integration")

        # Auth_PG Status
        status_frame = ttk.LabelFrame(auth_pg_frame, text="Auth_PG Status", padding="10")
        status_frame.pack(fill=tk.X, padx=10, pady=10)

        self.auth_pg_status_var = tk.StringVar(value="Checking...")
        status_label = ttk.Label(status_frame, textvariable=self.auth_pg_status_var)
        status_label.pack(side=tk.LEFT)

        ttk.Button(status_frame, text="Check Status",
                  command=self.check_auth_pg_status).pack(side=tk.RIGHT, padx=5)

        # Driver Management
        driver_frame = ttk.LabelFrame(auth_pg_frame, text="Driver Management", padding="10")
        driver_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(driver_frame, text="Extract Driver Support",
                  command=self.extract_driver_support).pack(side=tk.LEFT, padx=5)
        ttk.Button(driver_frame, text="Install Drivers",
                  command=self.install_auth_pg_drivers).pack(side=tk.LEFT, padx=5)
        ttk.Button(driver_frame, text="Apply Driver Fix",
                  command=self.apply_driver_fix).pack(side=tk.LEFT, padx=5)

        # Authentication Control
        auth_frame = ttk.LabelFrame(auth_pg_frame, text="Authentication Control", padding="10")
        auth_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(auth_frame, text="Launch Auth_PG v5",
                  command=self.launch_auth_pg,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=5)
        ttk.Button(auth_frame, text="View Instructions",
                  command=self.view_auth_pg_instructions).pack(side=tk.LEFT, padx=5)

        # USB Security (Optional)
        usb_frame = ttk.LabelFrame(auth_pg_frame, text="USB Security (Optional)", padding="10")
        usb_frame.pack(fill=tk.X, padx=10, pady=10)

        self.usb_security_var = tk.BooleanVar()
        usb_check = ttk.Checkbutton(usb_frame, text="Enable USB Security",
                                   variable=self.usb_security_var,
                                   command=self.toggle_usb_security)
        usb_check.pack(side=tk.LEFT)

        # Complete Integration
        complete_frame = ttk.LabelFrame(auth_pg_frame, text="Complete Integration", padding="10")
        complete_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(complete_frame, text="Complete Progress + Auth_PG Setup",
                  command=self.run_complete_integrated_setup,
                  style="Accent.TButton").pack(pady=10)

        # Auth_PG Log
        auth_log_frame = ttk.LabelFrame(auth_pg_frame, text="Auth_PG Operations Log", padding="10")
        auth_log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.auth_pg_log_text = scrolledtext.ScrolledText(auth_log_frame, height=12, width=70)
        self.auth_pg_log_text.pack(fill=tk.BOTH, expand=True)

    def log_message(self, message, log_widget=None):
        """Log a message to the specified log widget."""
        if log_widget is None:
            log_widget = getattr(self, 'log_text', None)

        if log_widget:
            log_widget.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n")
            log_widget.see(tk.END)
            self.root.update()

    def install_log(self, message):
        """Log a message to the installation log."""
        self.log_message(message, self.install_log_text)

    def auth_pg_log(self, message):
        """Log a message to the Auth_PG log."""
        self.log_message(message, self.auth_pg_log_text)

    def run_command(self, command, log_func=None):
        """Run a command with error handling and logging."""
        if log_func is None:
            log_func = self.log_message

        try:
            log_func(f"Running: {command}")
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                log_func(f"Success: {command}")
                return True, result.stdout
            else:
                log_func(f"Error: {result.stderr}")
                return False, result.stderr
        except Exception as e:
            log_func(f"Exception: {str(e)}")
            return False, str(e)

    def update_status_color(self, label, status):
        """Update label color based on status."""
        if status == "Enabled":
            label.configure(foreground="green")
        elif status == "Disabled":
            label.configure(foreground="red")
        else:
            label.configure(foreground="orange")

    # Security feature status checking methods
    def check_defender_status(self):
        """Check Windows Defender status."""
        try:
            # Check via WMI if available
            if self.wmi_conn:
                try:
                    antivirus_products = self.wmi_conn.query("SELECT * FROM AntiVirusProduct", namespace="root\\SecurityCenter2")
                    for product in antivirus_products:
                        if "Windows Defender" in product.displayName:
                            state = product.productState
                            if state & 0x1000:
                                return "Enabled"
                            else:
                                return "Disabled"
                except Exception:
                    pass

            # Fallback: Check registry
            try:
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                   r"SOFTWARE\Policies\Microsoft\Windows Defender")
                value, _ = winreg.QueryValueEx(key, "DisableAntiSpyware")
                winreg.CloseKey(key)
                return "Disabled" if value == 1 else "Enabled"
            except FileNotFoundError:
                return "Enabled"

        except Exception as e:
            return f"Error: {str(e)[:20]}..."

    def check_vbs_status(self):
        """Check VBS status."""
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                               r"SYSTEM\CurrentControlSet\Control\DeviceGuard")
            value, _ = winreg.QueryValueEx(key, "EnableVirtualizationBasedSecurity")
            winreg.CloseKey(key)
            return "Enabled" if value == 1 else "Disabled"
        except FileNotFoundError:
            return "Disabled"
        except Exception as e:
            return f"Error: {str(e)[:20]}..."

    def check_hvci_status(self):
        """Check HVCI status."""
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                               r"SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios\HypervisorEnforcedCodeIntegrity")
            value, _ = winreg.QueryValueEx(key, "Enabled")
            winreg.CloseKey(key)
            return "Enabled" if value == 1 else "Disabled"
        except FileNotFoundError:
            return "Disabled"
        except Exception as e:
            return f"Error: {str(e)[:20]}..."

    def check_windows_update_status(self):
        """Check Windows Update status."""
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                               r"SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU")
            value, _ = winreg.QueryValueEx(key, "NoAutoUpdate")
            winreg.CloseKey(key)
            return "Disabled" if value == 1 else "Enabled"
        except FileNotFoundError:
            return "Enabled"
        except Exception as e:
            return f"Error: {str(e)[:20]}..."

    def check_firewall_status(self):
        """Check Windows Firewall status."""
        try:
            result = subprocess.run("netsh advfirewall show allprofiles state",
                                  shell=True, capture_output=True, text=True)
            if "State                                 ON" in result.stdout:
                return "Enabled"
            else:
                return "Disabled"
        except Exception as e:
            return f"Error: {str(e)[:20]}..."

    def check_uac_status(self):
        """Check UAC status."""
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                               r"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System")
            value, _ = winreg.QueryValueEx(key, "EnableLUA")
            winreg.CloseKey(key)
            return "Enabled" if value == 1 else "Disabled"
        except FileNotFoundError:
            return "Enabled"
        except Exception as e:
            return f"Error: {str(e)[:20]}..."

    def refresh_all_status(self):
        """Refresh all security feature statuses."""
        self.status_var.set("Refreshing all statuses...")

        status_methods = {
            'defender': self.check_defender_status,
            'vbs': self.check_vbs_status,
            'hvci': self.check_hvci_status,
            'windows_update': self.check_windows_update_status,
            'firewall': self.check_firewall_status,
            'uac': self.check_uac_status
        }

        for feature, method in status_methods.items():
            if feature in self.security_features:
                status = method()
                self.security_features[feature]['status_var'].set(status)
                self.update_status_color(self.security_features[feature]['status_label'], status)

        self.status_var.set("All statuses updated")

    def toggle_security_feature(self, feature):
        """Toggle a specific security feature."""
        current_status = self.security_features[feature]['status_var'].get()

        if feature == 'defender':
            self.toggle_defender(current_status)
        elif feature == 'vbs':
            self.toggle_vbs(current_status)
        elif feature == 'hvci':
            self.toggle_hvci(current_status)
        elif feature == 'windows_update':
            self.toggle_windows_update(current_status)
        elif feature == 'firewall':
            self.toggle_firewall(current_status)
        elif feature == 'uac':
            self.toggle_uac(current_status)

        # Refresh status after toggle
        self.refresh_all_status()

    # Security toggle methods
    def toggle_defender(self, current_status):
        """Toggle Windows Defender."""
        if current_status == "Enabled":
            commands = [
                'reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender" /v "DisableAntiSpyware" /t REG_DWORD /d 1 /f',
                'reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Real-Time Protection" /v "DisableRealtimeMonitoring" /t REG_DWORD /d 1 /f'
            ]
            action = "Disabling"
        else:
            commands = [
                'reg delete "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender" /v "DisableAntiSpyware" /f',
                'reg delete "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Real-Time Protection" /v "DisableRealtimeMonitoring" /f'
            ]
            action = "Enabling"

        self.status_var.set(f"{action} Windows Defender...")
        success = all(self.run_command(cmd)[0] for cmd in commands)

        if success:
            messagebox.showinfo("Success", f"Windows Defender {action.lower()} completed.")
        else:
            messagebox.showerror("Error", f"Failed to toggle Windows Defender.")

    def toggle_vbs(self, current_status):
        """Toggle VBS."""
        if current_status == "Enabled":
            command = 'reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\DeviceGuard" /v "EnableVirtualizationBasedSecurity" /t REG_DWORD /d 0 /f'
            action = "Disabling"
        else:
            command = 'reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\DeviceGuard" /v "EnableVirtualizationBasedSecurity" /t REG_DWORD /d 1 /f'
            action = "Enabling"

        self.status_var.set(f"{action} VBS...")
        success, _ = self.run_command(command)

        if success:
            messagebox.showinfo("Success", f"VBS {action.lower()} completed. Restart required.")
        else:
            messagebox.showerror("Error", f"Failed to toggle VBS.")

    def toggle_hvci(self, current_status):
        """Toggle HVCI."""
        if current_status == "Enabled":
            command = 'reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\DeviceGuard\\Scenarios\\HypervisorEnforcedCodeIntegrity" /v "Enabled" /t REG_DWORD /d 0 /f'
            action = "Disabling"
        else:
            command = 'reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\DeviceGuard\\Scenarios\\HypervisorEnforcedCodeIntegrity" /v "Enabled" /t REG_DWORD /d 1 /f'
            action = "Enabling"

        self.status_var.set(f"{action} HVCI...")
        success, _ = self.run_command(command)

        if success:
            messagebox.showinfo("Success", f"HVCI {action.lower()} completed. Restart required.")
        else:
            messagebox.showerror("Error", f"Failed to toggle HVCI.")

    def toggle_windows_update(self, current_status):
        """Toggle Windows Update."""
        if current_status == "Enabled":
            command = 'reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU" /v "NoAutoUpdate" /t REG_DWORD /d 1 /f'
            action = "Disabling"
        else:
            command = 'reg delete "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU" /v "NoAutoUpdate" /f'
            action = "Enabling"

        self.status_var.set(f"{action} Windows Update...")
        success, _ = self.run_command(command)

        if success:
            messagebox.showinfo("Success", f"Windows Update {action.lower()} completed.")
        else:
            messagebox.showerror("Error", f"Failed to toggle Windows Update.")

    def toggle_firewall(self, current_status):
        """Toggle Windows Firewall."""
        if current_status == "Enabled":
            command = 'netsh advfirewall set allprofiles state off'
            action = "Disabling"
        else:
            command = 'netsh advfirewall set allprofiles state on'
            action = "Enabling"

        self.status_var.set(f"{action} Windows Firewall...")
        success, _ = self.run_command(command)

        if success:
            messagebox.showinfo("Success", f"Windows Firewall {action.lower()} completed.")
        else:
            messagebox.showerror("Error", f"Failed to toggle Windows Firewall.")

    def toggle_uac(self, current_status):
        """Toggle UAC."""
        if current_status == "Enabled":
            command = 'reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v "EnableLUA" /t REG_DWORD /d 0 /f'
            action = "Disabling"
        else:
            command = 'reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v "EnableLUA" /t REG_DWORD /d 1 /f'
            action = "Enabling"

        self.status_var.set(f"{action} UAC...")
        success, _ = self.run_command(command)

        if success:
            messagebox.showinfo("Success", f"UAC {action.lower()} completed. Restart required.")
        else:
            messagebox.showerror("Error", f"Failed to toggle UAC.")

    def disable_all_security(self):
        """Disable all security features."""
        if messagebox.askyesno("Confirm", "This will disable ALL security features. Continue?"):
            for feature in self.security_features:
                current_status = self.security_features[feature]['status_var'].get()
                if current_status == "Enabled":
                    self.toggle_security_feature(feature)

    def enable_all_security(self):
        """Enable all security features."""
        if messagebox.askyesno("Confirm", "This will enable ALL security features. Continue?"):
            for feature in self.security_features:
                current_status = self.security_features[feature]['status_var'].get()
                if current_status == "Disabled":
                    self.toggle_security_feature(feature)

    # System optimization methods
    def optimize_services(self):
        """Disable unnecessary Windows services."""
        services = ["DiagTrack", "dmwappushservice", "SysMain", "WSearch"]

        self.log_message("Starting service optimization...")
        for service in services:
            command = f'sc config "{service}" start= disabled'
            success, _ = self.run_command(command)
            if success:
                self.log_message(f"Disabled service: {service}")
            else:
                self.log_message(f"Failed to disable service: {service}")

        messagebox.showinfo("Complete", "Service optimization completed. Check log for details.")

    def restore_services(self):
        """Restore default service settings."""
        services = {
            "DiagTrack": "auto",
            "dmwappushservice": "manual",
            "SysMain": "auto",
            "WSearch": "auto"
        }

        self.log_message("Restoring default services...")
        for service, start_type in services.items():
            command = f'sc config "{service}" start= {start_type}'
            success, _ = self.run_command(command)
            if success:
                self.log_message(f"Restored service: {service} to {start_type}")
            else:
                self.log_message(f"Failed to restore service: {service}")

        messagebox.showinfo("Complete", "Service restoration completed. Check log for details.")

    def apply_performance_tweaks(self):
        """Apply performance registry tweaks."""
        tweaks = [
            ('reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\VisualEffects" /v "VisualFXSetting" /t REG_DWORD /d 2 /f', "Visual effects for performance"),
            ('reg add "HKCU\\Control Panel\\Desktop" /v "MenuShowDelay" /t REG_SZ /d "0" /f', "Menu show delay"),
            ('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management" /v "LargeSystemCache" /t REG_DWORD /d 1 /f', "Large system cache"),
            ('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management" /v "IoPageLockLimit" /t REG_DWORD /d 983040 /f', "IO page lock limit")
        ]

        self.log_message("Applying performance tweaks...")
        for command, description in tweaks:
            success, _ = self.run_command(command)
            if success:
                self.log_message(f"Applied: {description}")
            else:
                self.log_message(f"Failed: {description}")

        messagebox.showinfo("Complete", "Performance tweaks applied. Check log for details.")

    def set_high_performance(self):
        """Set high performance power plan."""
        command = 'powercfg -setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c'
        success, _ = self.run_command(command)

        if success:
            self.log_message("High performance power plan activated")
            messagebox.showinfo("Success", "High performance power plan activated.")
        else:
            self.log_message("Failed to set high performance power plan")
            messagebox.showerror("Error", "Failed to set high performance power plan.")

    def disable_game_dvr(self):
        """Disable Game DVR and related features."""
        commands = [
            ('reg add "HKCU\\System\\GameConfigStore" /v "GameDVR_Enabled" /t REG_DWORD /d 0 /f', "Game DVR"),
            ('reg add "HKCU\\System\\GameConfigStore" /v "GameDVR_FSEBehaviorMode" /t REG_DWORD /d 2 /f', "Game DVR FSE behavior"),
            ('reg add "HKCU\\Software\\Microsoft\\GameBar" /v "AutoGameModeEnabled" /t REG_DWORD /d 0 /f', "Auto game mode"),
            ('reg add "HKCU\\Software\\Microsoft\\GameBar" /v "UseNexusForGameBarEnabled" /t REG_DWORD /d 0 /f', "Game bar nexus")
        ]

        self.log_message("Disabling Game DVR...")
        for command, description in commands:
            success, _ = self.run_command(command)
            if success:
                self.log_message(f"Disabled: {description}")
            else:
                self.log_message(f"Failed to disable: {description}")

        messagebox.showinfo("Complete", "Game DVR disabled. Check log for details.")

    def disable_fullscreen_opt(self):
        """Disable fullscreen optimizations."""
        command = 'reg add "HKCU\\System\\GameConfigStore" /v "GameDVR_DXGIHonorFSEWindowsCompatible" /t REG_DWORD /d 1 /f'
        success, _ = self.run_command(command)

        if success:
            self.log_message("Fullscreen optimizations disabled")
            messagebox.showinfo("Success", "Fullscreen optimizations disabled.")
        else:
            self.log_message("Failed to disable fullscreen optimizations")
            messagebox.showerror("Error", "Failed to disable fullscreen optimizations.")

    # Progress environment setup methods
    def create_progress_dirs(self):
        """Create Progress software directories."""
        directories = [
            "C:\\Progress\\OpenEdge",
            "C:\\Progress\\OpenEdge\\bin",
            "C:\\Progress\\Injection",
            "C:\\Progress\\Tools"
        ]

        self.log_message("Creating Progress directories...")
        for directory in directories:
            try:
                os.makedirs(directory, exist_ok=True)
                self.log_message(f"Created directory: {directory}")
            except Exception as e:
                self.log_message(f"Failed to create directory {directory}: {e}")

        messagebox.showinfo("Complete", "Progress directories created. Check log for details.")

    def set_progress_env_vars(self):
        """Set Progress environment variables."""
        env_vars = [
            ('setx PROGRESS_DLC "C:\\Progress\\OpenEdge" /M', "PROGRESS_DLC"),
            ('setx PATH "%PATH%;C:\\Progress\\OpenEdge\\bin;C:\\Progress\\Injection" /M', "PATH update")
        ]

        self.log_message("Setting Progress environment variables...")
        for command, description in env_vars:
            success, _ = self.run_command(command)
            if success:
                self.log_message(f"Set environment variable: {description}")
            else:
                self.log_message(f"Failed to set: {description}")

        messagebox.showinfo("Complete", "Environment variables set. Restart may be required.")

    def create_progress_registry(self):
        """Create Progress registry entries."""
        registry_commands = [
            ('reg add "HKLM\\SOFTWARE\\Progress Software" /f', "Progress Software key"),
            ('reg add "HKLM\\SOFTWARE\\Progress Software\\Progress OpenEdge" /f', "Progress OpenEdge key"),
            ('reg add "HKLM\\SOFTWARE\\Progress Software\\Progress OpenEdge\\11.7" /v "DLC" /t REG_SZ /d "C:\\Progress\\OpenEdge" /f', "DLC path")
        ]

        self.log_message("Creating Progress registry entries...")
        for command, description in registry_commands:
            success, _ = self.run_command(command)
            if success:
                self.log_message(f"Created registry entry: {description}")
            else:
                self.log_message(f"Failed to create: {description}")

        # Create configuration files
        try:
            with open("C:\\Progress\\OpenEdge\\progress.cfg", "w") as f:
                f.write("dlc=C:\\Progress\\OpenEdge\n")
            self.log_message("Created progress.cfg")

            with open("C:\\Progress\\Injection\\config.ini", "w") as f:
                f.write("injection_path=C:\\Progress\\Injection\n")
            self.log_message("Created config.ini")
        except Exception as e:
            self.log_message(f"Failed to create config files: {e}")

        messagebox.showinfo("Complete", "Progress registry entries created. Check log for details.")

    def run_complete_progress_setup(self):
        """Run the complete Progress software setup."""
        if messagebox.askyesno("Confirm", "This will run the complete Progress setup including:\n"
                                        "- Create directories\n"
                                        "- Set environment variables\n"
                                        "- Create registry entries\n"
                                        "- Apply optimizations\n\n"
                                        "For Auth_PG integration, use the 'Complete Progress + Auth_PG Setup' button in the Auth_PG tab.\n\n"
                                        "Continue?"):

            self.log_message("=== Starting Complete Progress Setup ===")

            # Create directories
            self.create_progress_dirs()

            # Set environment variables
            self.set_progress_env_vars()

            # Create registry entries
            self.create_progress_registry()

            # Apply optimizations
            self.optimize_services()
            self.apply_performance_tweaks()
            self.disable_game_dvr()
            self.set_high_performance()

            self.log_message("=== Complete Progress Setup Finished ===")
            messagebox.showinfo("Complete", "Complete Progress setup finished! Check the log for details.\n\n"
                                          "For Auth_PG integration, go to the Auth_PG Integration tab.")

    # Installation methods
    def install_directx(self):
        """Install DirectX Web Installer."""
        def install_thread():
            self.install_log("Starting DirectX installation...")

            # Create temp directory
            temp_dir = os.path.join(os.environ['TEMP'], 'progress_setup')
            os.makedirs(temp_dir, exist_ok=True)

            # Download DirectX
            download_cmd = f'powershell -Command "Invoke-WebRequest -Uri \'https://download.microsoft.com/download/1/7/1/1718CCC4-6315-4D8E-9543-8E28A4E18C4C/dxwebsetup.exe\' -OutFile \'{temp_dir}\\dxwebsetup.exe\'"'
            success, _ = self.run_command(download_cmd, self.install_log)

            if success:
                # Install DirectX
                install_cmd = f'start /wait {temp_dir}\\dxwebsetup.exe /Q'
                success, _ = self.run_command(install_cmd, self.install_log)

                if success:
                    self.install_log("DirectX installation completed successfully")
                else:
                    self.install_log("DirectX installation failed")
            else:
                self.install_log("Failed to download DirectX installer")

            # Cleanup
            try:
                os.remove(f'{temp_dir}\\dxwebsetup.exe')
            except:
                pass

        # Run in thread to prevent GUI freezing
        threading.Thread(target=install_thread, daemon=True).start()
        messagebox.showinfo("Started", "DirectX installation started. Check the installation log for progress.")

    def install_vcredist(self):
        """Install Visual C++ Redistributables."""
        def install_thread():
            self.install_log("Starting Visual C++ Redistributables installation...")

            # Create temp directory
            temp_dir = os.path.join(os.environ['TEMP'], 'progress_setup')
            os.makedirs(temp_dir, exist_ok=True)

            # Download and install x64
            download_x64 = f'powershell -Command "Invoke-WebRequest -Uri \'https://aka.ms/vs/17/release/vc_redist.x64.exe\' -OutFile \'{temp_dir}\\vc_redist.x64.exe\'"'
            success, _ = self.run_command(download_x64, self.install_log)

            if success:
                install_x64 = f'start /wait {temp_dir}\\vc_redist.x64.exe /passive /norestart'
                self.run_command(install_x64, self.install_log)

            # Download and install x86
            download_x86 = f'powershell -Command "Invoke-WebRequest -Uri \'https://aka.ms/vs/17/release/vc_redist.x86.exe\' -OutFile \'{temp_dir}\\vc_redist.x86.exe\'"'
            success, _ = self.run_command(download_x86, self.install_log)

            if success:
                install_x86 = f'start /wait {temp_dir}\\vc_redist.x86.exe /passive /norestart'
                self.run_command(install_x86, self.install_log)

            self.install_log("Visual C++ Redistributables installation completed")

            # Cleanup
            try:
                os.remove(f'{temp_dir}\\vc_redist.x64.exe')
                os.remove(f'{temp_dir}\\vc_redist.x86.exe')
            except:
                pass

        # Run in thread to prevent GUI freezing
        threading.Thread(target=install_thread, daemon=True).start()
        messagebox.showinfo("Started", "Visual C++ installation started. Check the installation log for progress.")

    def clean_temp_files(self):
        """Clean temporary files."""
        temp_dirs = [
            os.environ.get('TEMP', ''),
            os.environ.get('TMP', ''),
            'C:\\Windows\\Temp'
        ]

        self.install_log("Starting temporary file cleanup...")

        for temp_dir in temp_dirs:
            if temp_dir and os.path.exists(temp_dir):
                try:
                    # Use Windows built-in disk cleanup
                    command = f'cleanmgr /sagerun:1'
                    self.run_command(command, self.install_log)
                    self.install_log(f"Cleaned temporary files in {temp_dir}")
                except Exception as e:
                    self.install_log(f"Failed to clean {temp_dir}: {e}")

        # Also clean our own temp directory
        try:
            temp_setup_dir = os.path.join(os.environ['TEMP'], 'progress_setup')
            if os.path.exists(temp_setup_dir):
                import shutil
                shutil.rmtree(temp_setup_dir)
                self.install_log("Cleaned progress_setup temp directory")
        except Exception as e:
            self.install_log(f"Failed to clean progress_setup directory: {e}")

        messagebox.showinfo("Complete", "Temporary file cleanup completed.")

    def run_sfc_scan(self):
        """Run System File Checker scan."""
        def sfc_thread():
            self.install_log("Starting System File Checker scan...")
            self.install_log("This may take several minutes...")

            command = 'sfc /scannow'
            success, output = self.run_command(command, self.install_log)

            if success:
                self.install_log("System File Checker scan completed successfully")
                if "found corrupt files" in output.lower():
                    self.install_log("Corrupt files were found and repaired")
                else:
                    self.install_log("No integrity violations found")
            else:
                self.install_log("System File Checker scan failed")

        # Run in thread to prevent GUI freezing
        threading.Thread(target=sfc_thread, daemon=True).start()
        messagebox.showinfo("Started", "System File Checker scan started. This may take several minutes. Check the installation log for progress.")

    # Auth_PG Integration Methods
    def check_auth_pg_status(self):
        """Check Auth_PG installation and driver status."""
        self.auth_pg_log("Checking Auth_PG status...")

        auth_pg_config = self.config.get("auth_pg_integration", {})
        auth_pg_dir = auth_pg_config.get("auth_pg_directory", "Auth_PG v5")
        auth_pg_exe = auth_pg_config.get("executable", "Auth_PG v5.exe")

        # Check if Auth_PG directory exists
        if os.path.exists(auth_pg_dir):
            self.auth_pg_log(f"Auth_PG directory found: {auth_pg_dir}")

            # Check if executable exists
            exe_path = os.path.join(auth_pg_dir, auth_pg_exe)
            if os.path.exists(exe_path):
                self.auth_pg_log(f"Auth_PG executable found: {exe_path}")
                self.auth_pg_status_var.set("Auth_PG Available")
            else:
                self.auth_pg_log(f"Auth_PG executable not found: {exe_path}")
                self.auth_pg_status_var.set("Executable Missing")
        else:
            self.auth_pg_log(f"Auth_PG directory not found: {auth_pg_dir}")
            self.auth_pg_status_var.set("Not Found")

        # Check driver status (simplified check)
        try:
            # Check for common driver-related registry entries or services
            result = subprocess.run("sc query | findstr /i auth", shell=True, capture_output=True, text=True)
            if "RUNNING" in result.stdout:
                self.auth_pg_log("Auth_PG related services detected")
            else:
                self.auth_pg_log("No Auth_PG services detected")
        except Exception as e:
            self.auth_pg_log(f"Driver status check failed: {e}")

    def extract_driver_support(self):
        """Extract Driver Support One.rar."""
        def extract_thread():
            self.auth_pg_log("Starting driver support extraction...")

            auth_pg_config = self.config.get("auth_pg_integration", {})
            auth_pg_dir = auth_pg_config.get("auth_pg_directory", "Auth_PG v5")
            driver_rar = auth_pg_config.get("driver_support", "Driver Support One.rar")
            temp_dir = auth_pg_config.get("temp_extract_dir", "C:\\Progress\\Auth_PG_Temp")

            driver_rar_path = os.path.join(auth_pg_dir, driver_rar)

            if not os.path.exists(driver_rar_path):
                self.auth_pg_log(f"Driver support file not found: {driver_rar_path}")
                return

            # Create temp directory
            os.makedirs(temp_dir, exist_ok=True)

            # Try to extract using WinRAR or 7-Zip
            extract_commands = [
                f'winrar x "{driver_rar_path}" "{temp_dir}\\"',
                f'7z x "{driver_rar_path}" -o"{temp_dir}"',
                f'powershell -Command "Expand-Archive -Path \'{driver_rar_path}\' -DestinationPath \'{temp_dir}\'"'
            ]

            extracted = False
            for cmd in extract_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                    if result.returncode == 0:
                        self.auth_pg_log(f"Successfully extracted driver support to: {temp_dir}")
                        extracted = True
                        break
                except Exception as e:
                    continue

            if not extracted:
                self.auth_pg_log("Failed to extract driver support. Please install WinRAR or 7-Zip.")
            else:
                self.auth_pg_log("Driver support extraction completed")

        threading.Thread(target=extract_thread, daemon=True).start()
        messagebox.showinfo("Started", "Driver support extraction started. Check the Auth_PG log for progress.")

    def install_auth_pg_drivers(self):
        """Install Auth_PG drivers."""
        def install_thread():
            self.auth_pg_log("Starting Auth_PG driver installation...")

            auth_pg_config = self.config.get("auth_pg_integration", {})
            temp_dir = auth_pg_config.get("temp_extract_dir", "C:\\Progress\\Auth_PG_Temp")

            if not os.path.exists(temp_dir):
                self.auth_pg_log("Driver support not extracted. Please extract driver support first.")
                return

            # Look for driver installation files
            driver_files = []
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    if file.lower().endswith(('.inf', '.sys', '.exe', '.msi')):
                        driver_files.append(os.path.join(root, file))

            if not driver_files:
                self.auth_pg_log("No driver files found in extracted directory")
                return

            self.auth_pg_log(f"Found {len(driver_files)} driver files")

            # Install drivers
            for driver_file in driver_files:
                self.auth_pg_log(f"Installing: {os.path.basename(driver_file)}")

                if driver_file.lower().endswith('.inf'):
                    # Install INF file
                    cmd = f'pnputil /add-driver "{driver_file}" /install'
                elif driver_file.lower().endswith('.exe'):
                    # Run executable installer
                    cmd = f'"{driver_file}" /S /silent'
                elif driver_file.lower().endswith('.msi'):
                    # Install MSI package
                    cmd = f'msiexec /i "{driver_file}" /quiet'
                else:
                    continue

                success, output = self.run_command(cmd, self.auth_pg_log)
                if success:
                    self.auth_pg_log(f"Successfully installed: {os.path.basename(driver_file)}")
                else:
                    self.auth_pg_log(f"Failed to install: {os.path.basename(driver_file)}")

            self.auth_pg_log("Driver installation process completed")

        threading.Thread(target=install_thread, daemon=True).start()
        messagebox.showinfo("Started", "Auth_PG driver installation started. Check the Auth_PG log for progress.")

    def apply_driver_fix(self):
        """Apply driver fix from FIX.rar."""
        def fix_thread():
            self.auth_pg_log("Applying Auth_PG driver fix...")

            auth_pg_config = self.config.get("auth_pg_integration", {})
            auth_pg_dir = auth_pg_config.get("auth_pg_directory", "Auth_PG v5")
            fix_rar = auth_pg_config.get("fix_package", "FIX.rar")
            temp_dir = auth_pg_config.get("temp_extract_dir", "C:\\Progress\\Auth_PG_Temp")

            fix_rar_path = os.path.join(auth_pg_dir, fix_rar)

            if not os.path.exists(fix_rar_path):
                self.auth_pg_log(f"Fix package not found: {fix_rar_path}")
                return

            # Create temp directory for fix
            fix_temp_dir = os.path.join(temp_dir, "fix")
            os.makedirs(fix_temp_dir, exist_ok=True)

            # Extract fix package
            extract_commands = [
                f'winrar x "{fix_rar_path}" "{fix_temp_dir}\\"',
                f'7z x "{fix_rar_path}" -o"{fix_temp_dir}"'
            ]

            extracted = False
            for cmd in extract_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                    if result.returncode == 0:
                        self.auth_pg_log("Successfully extracted fix package")
                        extracted = True
                        break
                except Exception:
                    continue

            if not extracted:
                self.auth_pg_log("Failed to extract fix package")
                return

            # Apply fixes
            for root, dirs, files in os.walk(fix_temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    if file.lower().endswith('.exe'):
                        self.auth_pg_log(f"Running fix: {file}")
                        cmd = f'"{file_path}" /S /silent'
                        success, _ = self.run_command(cmd, self.auth_pg_log)
                        if success:
                            self.auth_pg_log(f"Fix applied: {file}")
                        else:
                            self.auth_pg_log(f"Fix failed: {file}")
                    elif file.lower().endswith('.bat'):
                        self.auth_pg_log(f"Running batch fix: {file}")
                        cmd = f'"{file_path}"'
                        success, _ = self.run_command(cmd, self.auth_pg_log)

            self.auth_pg_log("Driver fix process completed")

        threading.Thread(target=fix_thread, daemon=True).start()
        messagebox.showinfo("Started", "Auth_PG driver fix started. Check the Auth_PG log for progress.")

    def launch_auth_pg(self):
        """Launch Auth_PG v5 executable."""
        self.auth_pg_log("Launching Auth_PG v5...")

        auth_pg_config = self.config.get("auth_pg_integration", {})
        auth_pg_dir = auth_pg_config.get("auth_pg_directory", "Auth_PG v5")
        auth_pg_exe = auth_pg_config.get("executable", "Auth_PG v5.exe")

        exe_path = os.path.join(auth_pg_dir, auth_pg_exe)

        if not os.path.exists(exe_path):
            self.auth_pg_log(f"Auth_PG executable not found: {exe_path}")
            messagebox.showerror("Error", f"Auth_PG executable not found: {exe_path}")
            return

        try:
            # Launch Auth_PG as a separate process
            subprocess.Popen([exe_path], cwd=auth_pg_dir)
            self.auth_pg_log("Auth_PG v5 launched successfully")
            messagebox.showinfo("Success", "Auth_PG v5 launched successfully")
        except Exception as e:
            self.auth_pg_log(f"Failed to launch Auth_PG: {e}")
            messagebox.showerror("Error", f"Failed to launch Auth_PG: {e}")

    def view_auth_pg_instructions(self):
        """View Auth_PG instructions image."""
        auth_pg_config = self.config.get("auth_pg_integration", {})
        auth_pg_dir = auth_pg_config.get("auth_pg_directory", "Auth_PG v5")
        instructions_image = auth_pg_config.get("instructions_image", "change-2-eng.JPG")

        image_path = os.path.join(auth_pg_dir, instructions_image)

        if os.path.exists(image_path):
            try:
                # Open image with default viewer
                os.startfile(image_path)
                self.auth_pg_log("Opened Auth_PG instructions image")
            except Exception as e:
                self.auth_pg_log(f"Failed to open instructions image: {e}")
                messagebox.showerror("Error", f"Failed to open instructions: {e}")
        else:
            self.auth_pg_log(f"Instructions image not found: {image_path}")
            messagebox.showerror("Error", f"Instructions image not found: {image_path}")

    def toggle_usb_security(self):
        """Toggle USB security setting."""
        usb_enabled = self.usb_security_var.get()

        if usb_enabled:
            self.auth_pg_log("USB security enabled")
            messagebox.showinfo("USB Security", "USB security enabled. You can use a USB for added security.")
        else:
            self.auth_pg_log("USB security disabled")

        # Update config (in memory)
        if "auth_pg_integration" not in self.config:
            self.config["auth_pg_integration"] = {}
        self.config["auth_pg_integration"]["usb_security_enabled"] = usb_enabled

    def run_complete_integrated_setup(self):
        """Run complete Progress + Auth_PG integrated setup."""
        if messagebox.askyesno("Confirm", "This will run the complete integrated setup including:\n\n"
                                        "Progress Environment:\n"
                                        "- Create directories\n"
                                        "- Set environment variables\n"
                                        "- Create registry entries\n"
                                        "- Apply optimizations\n\n"
                                        "Auth_PG Integration:\n"
                                        "- Extract driver support\n"
                                        "- Install Auth_PG drivers\n"
                                        "- Apply driver fixes if needed\n"
                                        "- Prepare authentication\n\n"
                                        "Continue?"):

            def integrated_setup_thread():
                self.auth_pg_log("=== Starting Complete Integrated Setup ===")
                self.log_message("=== Starting Complete Integrated Setup ===")

                # Step 1: Progress Environment Setup
                self.auth_pg_log("Step 1: Setting up Progress environment...")
                self.create_progress_dirs()
                self.set_progress_env_vars()
                self.create_progress_registry()

                # Step 2: System Optimizations
                self.auth_pg_log("Step 2: Applying system optimizations...")
                self.optimize_services()
                self.apply_performance_tweaks()
                self.disable_game_dvr()
                self.set_high_performance()

                # Step 3: Auth_PG Integration
                self.auth_pg_log("Step 3: Setting up Auth_PG integration...")

                # Check Auth_PG status first
                self.check_auth_pg_status()

                # Extract driver support
                self.auth_pg_log("Extracting Auth_PG driver support...")
                auth_pg_config = self.config.get("auth_pg_integration", {})
                auth_pg_dir = auth_pg_config.get("auth_pg_directory", "Auth_PG v5")

                if os.path.exists(auth_pg_dir):
                    # Extract driver support
                    self.extract_driver_support()
                    time.sleep(3)  # Wait for extraction

                    # Install drivers
                    self.install_auth_pg_drivers()
                    time.sleep(2)  # Wait for installation

                    # Apply fixes if auto-fix is enabled
                    if auth_pg_config.get("auto_fix_drivers", True):
                        self.auth_pg_log("Auto-applying driver fixes...")
                        self.apply_driver_fix()
                        time.sleep(2)

                    self.auth_pg_log("Auth_PG integration completed")
                else:
                    self.auth_pg_log("Auth_PG directory not found - skipping Auth_PG setup")

                # Step 4: Final verification
                self.auth_pg_log("Step 4: Final verification...")
                self.check_auth_pg_status()
                self.refresh_all_status()

                self.auth_pg_log("=== Complete Integrated Setup Finished ===")
                self.log_message("=== Complete Integrated Setup Finished ===")

                # Show completion message
                messagebox.showinfo("Complete", "Complete integrated Progress + Auth_PG setup finished!\n\n"
                                              "Check both logs for details.\n"
                                              "You may need to restart for all changes to take effect.")

            # Run in thread to prevent GUI freezing
            threading.Thread(target=integrated_setup_thread, daemon=True).start()
            messagebox.showinfo("Started", "Complete integrated setup started. Check both logs for progress.")

    def run(self):
        """Start the GUI application."""
        self.root.mainloop()


def main():
    """Main entry point."""
    try:
        app = ComprehensiveSecurityToggle()
        app.run()
    except KeyboardInterrupt:
        pass
    except Exception as e:
        messagebox.showerror("Fatal Error", f"An unexpected error occurred: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
