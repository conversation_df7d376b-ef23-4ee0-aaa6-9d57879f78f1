#!/usr/bin/env python3
"""
SecureToggle Comprehensive - Complete Progress Software Setup Tool
Hardware-locked GUI application for comprehensive Windows security and optimization.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import os
import sys
import subprocess
import winreg
import platform
import uuid
import ctypes
import threading
import time
from pathlib import Path

# Try to import WMI, but handle gracefully if not available
try:
    import wmi
    WMI_AVAILABLE = True
except ImportError:
    WMI_AVAILABLE = False
    print("Warning: WMI module not available. Some features may be limited.")

class ComprehensiveSecurityToggle:
    def __init__(self):
        print("Starting SecureToggle Comprehensive...")
        
        self.root = tk.Tk()
        self.root.title("SecureToggle Comprehensive - Progress Setup Tool")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        print("Loading configuration...")
        # Load configuration and verify system lock
        self.config = self.load_config()
        
        print("Verifying system lock...")
        if not self.verify_system_lock():
            print("System lock verification failed!")
            messagebox.showerror("Access Denied", 
                               "This application is locked to a different system.\n"
                               "Hardware fingerprint mismatch detected.")
            sys.exit(1)
        
        print("Checking admin privileges...")
        # Check for admin privileges
        if not self.is_admin():
            print("Admin privileges required!")
            messagebox.showerror("Administrator Required", 
                               "This application requires administrator privileges.\n"
                               "Please run as administrator.")
            sys.exit(1)
        
        # Initialize WMI if available
        self.wmi_conn = None
        if WMI_AVAILABLE:
            try:
                self.wmi_conn = wmi.WMI()
            except Exception as e:
                print(f"Warning: Failed to initialize WMI: {e}")
                self.wmi_conn = None
        
        self.setup_ui()
        self.refresh_all_status()
    
    def load_config(self):
        """Load the lock configuration file."""
        config_path = Path(__file__).parent / "secure_toggle_lock_config.json"
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            messagebox.showerror("Configuration Error", 
                               "Lock configuration file not found.")
            sys.exit(1)
        except json.JSONDecodeError:
            messagebox.showerror("Configuration Error", 
                               "Invalid lock configuration file.")
            sys.exit(1)
    
    def verify_system_lock(self):
        """Verify system fingerprint against lock configuration."""
        if not self.config.get("system_lock", {}).get("lock_enabled", False):
            return True
        
        lock_config = self.config["system_lock"]
        
        # Get current system info
        current_hostname = platform.node()
        current_uuid = str(uuid.getnode())
        
        # Check hostname
        if current_hostname != lock_config.get("hostname"):
            return False
        
        # Check UUID (MAC-based)
        if current_uuid != lock_config.get("uuid"):
            return False
        
        return True
    
    def is_admin(self):
        """Check if running with administrator privileges."""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def setup_ui(self):
        """Setup the comprehensive user interface."""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Tab 1: Security Features
        self.setup_security_tab(notebook)
        
        # Tab 2: System Optimization
        self.setup_optimization_tab(notebook)
        
        # Tab 3: Progress Environment
        self.setup_progress_tab(notebook)
        
        # Tab 4: Installation Tools
        self.setup_installation_tab(notebook)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, 
                             relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=(0, 10))
    
    def setup_security_tab(self, notebook):
        """Setup the security features tab."""
        security_frame = ttk.Frame(notebook)
        notebook.add(security_frame, text="Security Features")
        
        # System info
        info_frame = ttk.LabelFrame(security_frame, text="System Information", padding="10")
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(info_frame, text=f"Hostname: {platform.node()}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"OS: {platform.system()} {platform.release()}").pack(anchor=tk.W)
        
        # Security features
        sec_frame = ttk.LabelFrame(security_frame, text="Security Controls", padding="10")
        sec_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Headers
        ttk.Label(sec_frame, text="Feature", font=("Arial", 10, "bold")).grid(row=0, column=0, padx=(0, 20), sticky=tk.W)
        ttk.Label(sec_frame, text="Status", font=("Arial", 10, "bold")).grid(row=0, column=1, padx=(0, 20))
        ttk.Label(sec_frame, text="Action", font=("Arial", 10, "bold")).grid(row=0, column=2)
        
        # Security features
        self.security_features = {}
        features = [
            ("defender", "Windows Defender"),
            ("vbs", "Virtualization-based Security"),
            ("hvci", "HVCI (Memory Integrity)"),
            ("windows_update", "Windows Update"),
            ("firewall", "Windows Firewall"),
            ("uac", "User Account Control")
        ]
        
        for i, (key, name) in enumerate(features, 1):
            status_var = tk.StringVar(value="Checking...")
            status_label = ttk.Label(sec_frame, textvariable=status_var)
            button = ttk.Button(sec_frame, text="Toggle", 
                              command=lambda k=key: self.toggle_security_feature(k))
            
            ttk.Label(sec_frame, text=name).grid(row=i, column=0, sticky=tk.W, pady=5)
            status_label.grid(row=i, column=1, pady=5)
            button.grid(row=i, column=2, pady=5)
            
            self.security_features[key] = {
                'status_var': status_var,
                'status_label': status_label,
                'button': button
            }
        
        # Control buttons
        button_frame = ttk.Frame(sec_frame)
        button_frame.grid(row=len(features)+1, column=0, columnspan=3, pady=20)
        
        ttk.Button(button_frame, text="Refresh All", 
                  command=self.refresh_all_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Disable All Security", 
                  command=self.disable_all_security).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Enable All Security", 
                  command=self.enable_all_security).pack(side=tk.LEFT, padx=5)
    
    def setup_optimization_tab(self, notebook):
        """Setup the system optimization tab."""
        opt_frame = ttk.Frame(notebook)
        notebook.add(opt_frame, text="System Optimization")
        
        # Services optimization
        services_frame = ttk.LabelFrame(opt_frame, text="Windows Services", padding="10")
        services_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(services_frame, text="Disable Unnecessary Services", 
                  command=self.optimize_services).pack(side=tk.LEFT, padx=5)
        ttk.Button(services_frame, text="Restore Default Services", 
                  command=self.restore_services).pack(side=tk.LEFT, padx=5)
        
        # Performance optimization
        perf_frame = ttk.LabelFrame(opt_frame, text="Performance Settings", padding="10")
        perf_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(perf_frame, text="Apply Performance Tweaks", 
                  command=self.apply_performance_tweaks).pack(side=tk.LEFT, padx=5)
        ttk.Button(perf_frame, text="Set High Performance Power Plan", 
                  command=self.set_high_performance).pack(side=tk.LEFT, padx=5)
        
        # Game optimization
        game_frame = ttk.LabelFrame(opt_frame, text="Gaming Optimizations", padding="10")
        game_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(game_frame, text="Disable Game DVR", 
                  command=self.disable_game_dvr).pack(side=tk.LEFT, padx=5)
        ttk.Button(game_frame, text="Disable Fullscreen Optimizations", 
                  command=self.disable_fullscreen_opt).pack(side=tk.LEFT, padx=5)
        
        # Output log
        log_frame = ttk.LabelFrame(opt_frame, text="Operation Log", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=70)
        self.log_text.pack(fill=tk.BOTH, expand=True)
    
    def setup_progress_tab(self, notebook):
        """Setup the Progress environment tab."""
        progress_frame = ttk.Frame(notebook)
        notebook.add(progress_frame, text="Progress Environment")
        
        # Environment setup
        env_frame = ttk.LabelFrame(progress_frame, text="Progress Environment Setup", padding="10")
        env_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(env_frame, text="Create Progress Directories", 
                  command=self.create_progress_dirs).pack(side=tk.LEFT, padx=5)
        ttk.Button(env_frame, text="Set Environment Variables", 
                  command=self.set_progress_env_vars).pack(side=tk.LEFT, padx=5)
        ttk.Button(env_frame, text="Create Registry Entries", 
                  command=self.create_progress_registry).pack(side=tk.LEFT, padx=5)
        
        # Full setup
        full_frame = ttk.LabelFrame(progress_frame, text="Complete Setup", padding="10")
        full_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(full_frame, text="Run Complete Progress Setup", 
                  command=self.run_complete_progress_setup, 
                  style="Accent.TButton").pack(pady=10)
        
        # Progress info
        info_frame = ttk.LabelFrame(progress_frame, text="Progress Information", padding="10")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        info_text = tk.Text(info_frame, height=15, wrap=tk.WORD)
        info_text.pack(fill=tk.BOTH, expand=True)
        
        info_content = """Progress Software Environment Setup:

1. Creates necessary directories:
   - C:\\Progress\\OpenEdge
   - C:\\Progress\\OpenEdge\\bin
   - C:\\Progress\\Injection
   - C:\\Progress\\Tools

2. Sets environment variables:
   - PROGRESS_DLC = C:\\Progress\\OpenEdge
   - Updates PATH with Progress directories

3. Creates registry entries for Progress OpenEdge

4. Generates configuration files

This setup prepares your system for Progress software injection and development."""
        
        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)
    
    def setup_installation_tab(self, notebook):
        """Setup the installation tools tab."""
        install_frame = ttk.Frame(notebook)
        notebook.add(install_frame, text="Installation Tools")
        
        # Dependencies
        deps_frame = ttk.LabelFrame(install_frame, text="System Dependencies", padding="10")
        deps_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(deps_frame, text="Install DirectX", 
                  command=self.install_directx).pack(side=tk.LEFT, padx=5)
        ttk.Button(deps_frame, text="Install Visual C++ Redistributables", 
                  command=self.install_vcredist).pack(side=tk.LEFT, padx=5)
        
        # System tools
        tools_frame = ttk.LabelFrame(install_frame, text="System Tools", padding="10")
        tools_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(tools_frame, text="Clean Temporary Files", 
                  command=self.clean_temp_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(tools_frame, text="System File Check", 
                  command=self.run_sfc_scan).pack(side=tk.LEFT, padx=5)
        
        # Installation log
        install_log_frame = ttk.LabelFrame(install_frame, text="Installation Log", padding="10")
        install_log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.install_log_text = scrolledtext.ScrolledText(install_log_frame, height=15, width=70)
        self.install_log_text.pack(fill=tk.BOTH, expand=True)

    def log_message(self, message, log_widget=None):
        """Log a message to the specified log widget."""
        if log_widget is None:
            log_widget = getattr(self, 'log_text', None)

        if log_widget:
            log_widget.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n")
            log_widget.see(tk.END)
            self.root.update()

    def install_log(self, message):
        """Log a message to the installation log."""
        self.log_message(message, self.install_log_text)

    def run_command(self, command, log_func=None):
        """Run a command with error handling and logging."""
        if log_func is None:
            log_func = self.log_message

        try:
            log_func(f"Running: {command}")
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                log_func(f"Success: {command}")
                return True, result.stdout
            else:
                log_func(f"Error: {result.stderr}")
                return False, result.stderr
        except Exception as e:
            log_func(f"Exception: {str(e)}")
            return False, str(e)

    def update_status_color(self, label, status):
        """Update label color based on status."""
        if status == "Enabled":
            label.configure(foreground="green")
        elif status == "Disabled":
            label.configure(foreground="red")
        else:
            label.configure(foreground="orange")

    # Security feature status checking methods
    def check_defender_status(self):
        """Check Windows Defender status."""
        try:
            # Check via WMI if available
            if self.wmi_conn:
                try:
                    antivirus_products = self.wmi_conn.query("SELECT * FROM AntiVirusProduct", namespace="root\\SecurityCenter2")
                    for product in antivirus_products:
                        if "Windows Defender" in product.displayName:
                            state = product.productState
                            if state & 0x1000:
                                return "Enabled"
                            else:
                                return "Disabled"
                except Exception:
                    pass

            # Fallback: Check registry
            try:
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                   r"SOFTWARE\Policies\Microsoft\Windows Defender")
                value, _ = winreg.QueryValueEx(key, "DisableAntiSpyware")
                winreg.CloseKey(key)
                return "Disabled" if value == 1 else "Enabled"
            except FileNotFoundError:
                return "Enabled"

        except Exception as e:
            return f"Error: {str(e)[:20]}..."

    def check_vbs_status(self):
        """Check VBS status."""
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                               r"SYSTEM\CurrentControlSet\Control\DeviceGuard")
            value, _ = winreg.QueryValueEx(key, "EnableVirtualizationBasedSecurity")
            winreg.CloseKey(key)
            return "Enabled" if value == 1 else "Disabled"
        except FileNotFoundError:
            return "Disabled"
        except Exception as e:
            return f"Error: {str(e)[:20]}..."

    def check_hvci_status(self):
        """Check HVCI status."""
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                               r"SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios\HypervisorEnforcedCodeIntegrity")
            value, _ = winreg.QueryValueEx(key, "Enabled")
            winreg.CloseKey(key)
            return "Enabled" if value == 1 else "Disabled"
        except FileNotFoundError:
            return "Disabled"
        except Exception as e:
            return f"Error: {str(e)[:20]}..."

    def check_windows_update_status(self):
        """Check Windows Update status."""
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                               r"SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU")
            value, _ = winreg.QueryValueEx(key, "NoAutoUpdate")
            winreg.CloseKey(key)
            return "Disabled" if value == 1 else "Enabled"
        except FileNotFoundError:
            return "Enabled"
        except Exception as e:
            return f"Error: {str(e)[:20]}..."

    def check_firewall_status(self):
        """Check Windows Firewall status."""
        try:
            result = subprocess.run("netsh advfirewall show allprofiles state",
                                  shell=True, capture_output=True, text=True)
            if "State                                 ON" in result.stdout:
                return "Enabled"
            else:
                return "Disabled"
        except Exception as e:
            return f"Error: {str(e)[:20]}..."

    def check_uac_status(self):
        """Check UAC status."""
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                               r"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System")
            value, _ = winreg.QueryValueEx(key, "EnableLUA")
            winreg.CloseKey(key)
            return "Enabled" if value == 1 else "Disabled"
        except FileNotFoundError:
            return "Enabled"
        except Exception as e:
            return f"Error: {str(e)[:20]}..."

    def refresh_all_status(self):
        """Refresh all security feature statuses."""
        self.status_var.set("Refreshing all statuses...")

        status_methods = {
            'defender': self.check_defender_status,
            'vbs': self.check_vbs_status,
            'hvci': self.check_hvci_status,
            'windows_update': self.check_windows_update_status,
            'firewall': self.check_firewall_status,
            'uac': self.check_uac_status
        }

        for feature, method in status_methods.items():
            if feature in self.security_features:
                status = method()
                self.security_features[feature]['status_var'].set(status)
                self.update_status_color(self.security_features[feature]['status_label'], status)

        self.status_var.set("All statuses updated")

    def toggle_security_feature(self, feature):
        """Toggle a specific security feature."""
        current_status = self.security_features[feature]['status_var'].get()

        if feature == 'defender':
            self.toggle_defender(current_status)
        elif feature == 'vbs':
            self.toggle_vbs(current_status)
        elif feature == 'hvci':
            self.toggle_hvci(current_status)
        elif feature == 'windows_update':
            self.toggle_windows_update(current_status)
        elif feature == 'firewall':
            self.toggle_firewall(current_status)
        elif feature == 'uac':
            self.toggle_uac(current_status)

        # Refresh status after toggle
        self.refresh_all_status()
