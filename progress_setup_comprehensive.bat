@echo off
echo ===================================================
echo Progress Software Setup and Optimization Script
echo ===================================================
echo.

REM Check for Administrator privileges
NET SESSION >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: This script requires Administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Running with Administrator privileges...
echo.

REM ===================================================
REM 1. Temporarily disable Windows Defender
REM ===================================================
echo Temporarily disabling Windows Defender...
reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows Defender" /v "DisableAntiSpyware" /t REG_DWORD /d 1 /f
reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection" /v "DisableRealtimeMonitoring" /t REG_DWORD /d 1 /f
echo.

REM ===================================================
REM 2. Disable Memory Integrity (Virtualization-based protection)
REM ===================================================
echo Disabling Memory Integrity (Virtualization-based protection)...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard" /v "EnableVirtualizationBasedSecurity" /t REG_DWORD /d 0 /f
reg add "HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios\HypervisorEnforcedCodeIntegrity" /v "Enabled" /t REG_DWORD /d 0 /f
echo.

REM ===================================================
REM 3. Install DirectX and Visual C++ Redistributables
REM ===================================================
echo Installing DirectX and Visual C++ Redistributables...
echo NOTE: This requires internet connection and will download required files.
echo.
echo Creating temporary directory...
mkdir "%TEMP%\progress_setup" 2>nul

echo Downloading DirectX Web Installer...
powershell -Command "& {Invoke-WebRequest -Uri 'https://download.microsoft.com/download/1/7/1/1718CCC4-6315-4D8E-9543-8E28A4E18C4C/dxwebsetup.exe' -OutFile '%TEMP%\progress_setup\dxwebsetup.exe'}"
echo Installing DirectX...
start /wait %TEMP%\progress_setup\dxwebsetup.exe /Q

echo Downloading Visual C++ Redistributables...
powershell -Command "& {Invoke-WebRequest -Uri 'https://aka.ms/vs/17/release/vc_redist.x64.exe' -OutFile '%TEMP%\progress_setup\vc_redist.x64.exe'}"
powershell -Command "& {Invoke-WebRequest -Uri 'https://aka.ms/vs/17/release/vc_redist.x86.exe' -OutFile '%TEMP%\progress_setup\vc_redist.x86.exe'}"
echo Installing Visual C++ Redistributables...
start /wait %TEMP%\progress_setup\vc_redist.x64.exe /passive /norestart
start /wait %TEMP%\progress_setup\vc_redist.x86.exe /passive /norestart
echo.

REM ===================================================
REM 4. Install Support Tools
REM ===================================================
echo Setting up Support Tools...
mkdir "C:\Progress\Tools" 2>nul
echo Support Tools directory created at C:\Progress\Tools
echo NOTE: You will need to manually download and install additional tools as needed.
echo.

REM ===================================================
REM 5. Secure Boot Warning
REM ===================================================
echo ===================================================
echo IMPORTANT: Secure Boot Configuration
echo ===================================================
echo This script cannot automatically disable Secure Boot as it requires BIOS changes.
echo.
echo Manual steps required:
echo 1. Restart your computer and enter BIOS/UEFI settings (usually F2, F10, F12, or Del key)
echo 2. Navigate to the Security or Boot section
echo 3. Find "Secure Boot" option and disable it
echo 4. Save changes and exit BIOS
echo.
echo Press any key to continue...
pause >nul
echo.

REM ===================================================
REM 6. Enable CPU Virtualization
REM ===================================================
echo ===================================================
echo CPU Virtualization Settings
echo ===================================================
echo NOTE: CPU Virtualization must be enabled in BIOS (VT-x for Intel, AMD-V for AMD)
echo This cannot be automated and requires manual BIOS configuration.
echo.
echo Configuring Windows settings for virtualization...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios" /v "HypervisorEnforcedCodeIntegrity" /t REG_DWORD /d 0 /f
reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\DeviceGuard" /v "EnableVirtualizationBasedSecurity" /t REG_DWORD /d 0 /f
echo.

REM ===================================================
REM 7. PC Optimization Tweaks
REM ===================================================
echo Applying PC optimization tweaks...

echo Disabling unnecessary services...
sc config "DiagTrack" start= disabled
sc config "dmwappushservice" start= disabled
sc config "SysMain" start= disabled
sc config "WSearch" start= disabled

echo Adjusting performance settings...
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" /v "VisualFXSetting" /t REG_DWORD /d 2 /f
reg add "HKCU\Control Panel\Desktop" /v "MenuShowDelay" /t REG_SZ /d "0" /f
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "LargeSystemCache" /t REG_DWORD /d 1 /f
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "IoPageLockLimit" /t REG_DWORD /d 983040 /f
echo.

REM ===================================================
REM 8. Game Optimization Settings
REM ===================================================
echo Applying game optimization settings...

echo Disabling fullscreen optimizations...
reg add "HKCU\System\GameConfigStore" /v "GameDVR_Enabled" /t REG_DWORD /d 0 /f
reg add "HKCU\System\GameConfigStore" /v "GameDVR_FSEBehaviorMode" /t REG_DWORD /d 2 /f
reg add "HKCU\Software\Microsoft\GameBar" /v "AutoGameModeEnabled" /t REG_DWORD /d 0 /f
reg add "HKCU\Software\Microsoft\GameBar" /v "UseNexusForGameBarEnabled" /t REG_DWORD /d 0 /f

echo Setting high performance power plan...
powercfg -setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c
echo.

REM ===================================================
REM 9. Progress Injection Environment Setup
REM ===================================================
echo Setting up Progress injection environment...

echo Creating Progress directories...
mkdir "C:\Progress\OpenEdge" 2>nul
mkdir "C:\Progress\OpenEdge\bin" 2>nul
mkdir "C:\Progress\Injection" 2>nul

echo Setting environment variables...
setx PROGRESS_DLC "C:\Progress\OpenEdge" /M
setx PATH "%PATH%;C:\Progress\OpenEdge\bin;C:\Progress\Injection" /M

echo Creating registry entries...
reg add "HKLM\SOFTWARE\Progress Software" /f
reg add "HKLM\SOFTWARE\Progress Software\Progress OpenEdge" /f
reg add "HKLM\SOFTWARE\Progress Software\Progress OpenEdge\11.7" /v "DLC" /t REG_SZ /d "C:\Progress\OpenEdge" /f

echo Creating configuration files...
echo dlc=C:\Progress\OpenEdge > "C:\Progress\OpenEdge\progress.cfg"
echo injection_path=C:\Progress\Injection > "C:\Progress\Injection\config.ini"
echo.

REM ===================================================
REM Cleanup and Final Notes
REM ===================================================
echo Cleaning up temporary files...
rmdir /s /q "%TEMP%\progress_setup" 2>nul

echo ===================================================
echo Setup Complete!
echo ===================================================
echo.
echo IMPORTANT NOTES:
echo 1. A system restart is REQUIRED for all changes to take effect
echo 2. Remember to manually disable Secure Boot in BIOS if not already done
echo 3. Ensure CPU Virtualization is enabled in BIOS
echo 4. After restart, you may need to manually install additional Progress components
echo.
echo Press any key to exit...
pause >nul