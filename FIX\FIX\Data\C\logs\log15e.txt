[2624:64A8][2025-03-02T01:34:17]i001: Burn v3.10.4.4718, Windows v10.0 (Build 19045: Service Pack 0), path: C:\Windows\Temp\{92874850-AD99-4A81-8DDA-036812A22CF3}\.cr\cp15e.exe
[2624:64A8][2025-03-02T01:34:17]i009: Command Line: '"-burn.clean.room=C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp15\cp15e.exe" -burn.filehandle.attached=568 -burn.filehandle.self=548 /quiet /repair /norestart /log "C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log15e.txt"'
[2624:64A8][2025-03-02T01:34:17]i000: Setting string variable 'WixBundleOriginalSource' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp15\cp15e.exe'
[2624:64A8][2025-03-02T01:34:17]i000: Setting string variable 'WixBundleOriginalSourceFolder' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp15\'
[2624:64A8][2025-03-02T01:34:17]i000: Setting string variable 'WixBundleLog' to value 'C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log15e.txt'
[2624:64A8][2025-03-02T01:34:17]i000: Setting string variable 'WixBundleName' to value 'Microsoft Visual C++ 2015-2019 Redistributable (x64) - 14.25.28508'
[2624:64A8][2025-03-02T01:34:17]i000: Setting string variable 'WixBundleManufacturer' to value 'Microsoft Corporation'
[2624:72DC][2025-03-02T01:34:17]i000: Setting version variable 'WixBundleFileVersion' to value '14.25.28508.3'
[2624:64A8][2025-03-02T01:34:17]i100: Detect begin, 10 packages
[2624:64A8][2025-03-02T01:34:17]i000: Setting version variable 'windows_uCRT_DetectKey' to value '10.0.19041.3636'
[2624:64A8][2025-03-02T01:34:17]i000: Setting numeric variable 'windows_uCRT_DetectKeyExists' to value 1
[2624:64A8][2025-03-02T01:34:17]i102: Detected related bundle: {804e7d66-ccc2-4c12-84ba-476da31d103d}, type: Upgrade, scope: PerMachine, version: 14.42.34433.0, operation: Downgrade
[2624:64A8][2025-03-02T01:34:17]i052: Condition '(VersionNT = v6.3 AND NOT VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[2624:64A8][2025-03-02T01:34:17]i052: Condition '(VersionNT = v6.3 AND VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[2624:64A8][2025-03-02T01:34:17]i052: Condition '(VersionNT = v6.2 AND NOT VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[2624:64A8][2025-03-02T01:34:17]i052: Condition '(VersionNT = v6.2 AND VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[2624:64A8][2025-03-02T01:34:17]i052: Condition '(VersionNT = v6.1 AND NOT VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[2624:64A8][2025-03-02T01:34:17]i052: Condition '(VersionNT = v6.1 AND VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[2624:64A8][2025-03-02T01:34:17]i052: Condition '(VersionNT = v6.0 AND NOT VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[2624:64A8][2025-03-02T01:34:17]i052: Condition '(VersionNT = v6.0 AND VersionNT64) AND (windows_uCRT_DetectKeyExists AND windows_uCRT_DetectKey >= v10.0.10240.0)' evaluates to false.
[2624:64A8][2025-03-02T01:34:17]i103: Detected related package: {382F1166-A409-4C5B-9B1E-85ED538B8291}, scope: PerMachine, version: 14.42.34433.0, language: 0 operation: Downgrade
[2624:64A8][2025-03-02T01:34:17]i103: Detected related package: {E1902FC6-C423-4719-AB8A-AC7B2694B367}, scope: PerMachine, version: 14.42.34433.0, language: 0 operation: Downgrade
[2624:64A8][2025-03-02T01:34:17]i101: Detected package: Windows81_x86, state: Absent, cached: None
[2624:64A8][2025-03-02T01:34:17]i101: Detected package: Windows81_x64, state: Absent, cached: None
[2624:64A8][2025-03-02T01:34:17]i101: Detected package: Windows8_x86, state: Absent, cached: None
[2624:64A8][2025-03-02T01:34:17]i101: Detected package: Windows8_x64, state: Absent, cached: None
[2624:64A8][2025-03-02T01:34:17]i101: Detected package: Windows7_MSU_x86, state: Absent, cached: None
[2624:64A8][2025-03-02T01:34:17]i101: Detected package: Windows7_MSU_x64, state: Absent, cached: None
[2624:64A8][2025-03-02T01:34:17]i101: Detected package: WindowsVista_MSU_x86, state: Absent, cached: None
[2624:64A8][2025-03-02T01:34:17]i101: Detected package: WindowsVista_MSU_x64, state: Absent, cached: None
[2624:64A8][2025-03-02T01:34:17]i101: Detected package: vcRuntimeMinimum_x64, state: Obsolete, cached: None
[2624:64A8][2025-03-02T01:34:17]i101: Detected package: vcRuntimeAdditional_x64, state: Obsolete, cached: None
[2624:64A8][2025-03-02T01:34:17]i052: Condition 'VersionNT64 >= v6.0 OR (VersionNT64 = v5.2 AND ServicePackLevel >= 1)' evaluates to true.
[2624:64A8][2025-03-02T01:34:17]i199: Detect complete, result: 0x0
[2624:72DC][2025-03-02T01:34:17]e000: Error 0x80070666: Cannot install a product when a newer version is installed.
[2624:64A8][2025-03-02T01:34:17]i500: Shutting down, exit code: 0x666
[2624:64A8][2025-03-02T01:34:17]i410: Variable: SystemFolder = C:\Windows\system32\
[2624:64A8][2025-03-02T01:34:17]i410: Variable: VersionNT = 10.0.0.0
[2624:64A8][2025-03-02T01:34:17]i410: Variable: VersionNT64 = 10.0.0.0
[2624:64A8][2025-03-02T01:34:17]i410: Variable: windows_uCRT_DetectKey = 10.0.19041.3636
[2624:64A8][2025-03-02T01:34:17]i410: Variable: windows_uCRT_DetectKeyExists = 1
[2624:64A8][2025-03-02T01:34:17]i410: Variable: WixBundleAction = 7
[2624:64A8][2025-03-02T01:34:17]i410: Variable: WixBundleElevated = 1
[2624:64A8][2025-03-02T01:34:17]i410: Variable: WixBundleFileVersion = 14.25.28508.3
[2624:64A8][2025-03-02T01:34:17]i410: Variable: WixBundleInstalled = 0
[2624:64A8][2025-03-02T01:34:17]i410: Variable: WixBundleLog = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\logs\log15e.txt
[2624:64A8][2025-03-02T01:34:17]i410: Variable: WixBundleManufacturer = Microsoft Corporation
[2624:64A8][2025-03-02T01:34:17]i410: Variable: WixBundleName = Microsoft Visual C++ 2015-2019 Redistributable (x64) - 14.25.28508
[2624:64A8][2025-03-02T01:34:17]i410: Variable: WixBundleOriginalSource = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp15\cp15e.exe
[2624:64A8][2025-03-02T01:34:17]i410: Variable: WixBundleOriginalSourceFolder = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp15\
[2624:64A8][2025-03-02T01:34:17]i410: Variable: WixBundleProviderKey = VC,redist.x64,amd64,14.25,bundle
[2624:64A8][2025-03-02T01:34:17]i410: Variable: WixBundleSourceProcessFolder = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp15\
[2624:64A8][2025-03-02T01:34:17]i410: Variable: WixBundleSourceProcessPath = C:\Users\<USER>\Desktop\DirectX 修复工具4.0全套C运行库\Data\C\cp15\cp15e.exe
[2624:64A8][2025-03-02T01:34:17]i410: Variable: WixBundleTag = 
[2624:64A8][2025-03-02T01:34:17]i410: Variable: WixBundleVersion = 14.25.28508.3
[2624:64A8][2025-03-02T01:34:17]i007: Exit code: 0x666, restarting: No
