#!/usr/bin/env python3
"""
Simple Secure Toggle - Clean interface for Progress/Auth_PG setup
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import threading
import os
import sys

class SimpleSecureToggle:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Simple Secure Toggle")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # Check admin privileges
        if not self.is_admin():
            messagebox.showerror("Admin Required", "This application requires administrator privileges.\nPlease run as administrator.")
            sys.exit(1)
        
        self.setup_ui()
        
    def is_admin(self):
        """Check if running with admin privileges."""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def setup_ui(self):
        """Setup the simple user interface."""
        # Main title
        title_label = tk.Label(self.root, text="Simple Secure Toggle", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        subtitle_label = tk.Label(self.root, text="For Progress/Auth_PG Setup", 
                                 font=("Arial", 10))
        subtitle_label.pack(pady=(0, 20))
        
        # Main buttons frame
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        # Simple toggle buttons
        tk.Button(button_frame, text="🔴 DISABLE Security (For Progress)", 
                 command=self.disable_all_security,
                 bg="#ff4444", fg="white", font=("Arial", 12, "bold"),
                 width=30, height=2).pack(pady=10)
        
        tk.Button(button_frame, text="🟢 ENABLE Security (Normal Use)", 
                 command=self.enable_all_security,
                 bg="#44ff44", fg="black", font=("Arial", 12, "bold"),
                 width=30, height=2).pack(pady=10)
        
        # Separator
        separator = ttk.Separator(self.root, orient='horizontal')
        separator.pack(fill='x', padx=20, pady=20)
        
        # Quick fix buttons
        quick_frame = tk.Frame(self.root)
        quick_frame.pack(pady=10)
        
        tk.Button(quick_frame, text="Fix Signature Error", 
                 command=self.fix_signature_error,
                 bg="#ffaa44", font=("Arial", 10),
                 width=20).pack(side=tk.LEFT, padx=5)
        
        tk.Button(quick_frame, text="Fix Invalidver Error", 
                 command=self.fix_invalidver_error,
                 bg="#ffaa44", font=("Arial", 10),
                 width=20).pack(side=tk.LEFT, padx=5)
        
        # Status display
        self.status_text = tk.Text(self.root, height=8, width=60)
        self.status_text.pack(pady=20, padx=20)
        
        # Add scrollbar to status
        scrollbar = tk.Scrollbar(self.status_text)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.status_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.status_text.yview)
        
        self.log("Simple Secure Toggle ready!")
        self.log("Click RED to disable security for Progress/Auth_PG")
        self.log("Click GREEN to re-enable security for normal use")
    
    def log(self, message):
        """Add message to status display."""
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.root.update()
    
    def run_command(self, command):
        """Run a command and return success status."""
        try:
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False
    
    def disable_all_security(self):
        """Disable all security features for Progress/Auth_PG."""
        if not messagebox.askyesno("Confirm", "This will DISABLE all security features for Progress/Auth_PG.\n\nContinue?"):
            return
        
        def disable_thread():
            self.log("🔴 DISABLING all security features...")
            
            # Disable Windows Defender
            self.log("Disabling Windows Defender...")
            self.run_command('reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender" /v "DisableAntiSpyware" /t REG_DWORD /d 1 /f')
            
            # Disable Firewall
            self.log("Disabling Windows Firewall...")
            self.run_command('netsh advfirewall set allprofiles state off')
            
            # Disable SmartScreen
            self.log("Disabling SmartScreen...")
            self.run_command('reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer" /v "SmartScreenEnabled" /t REG_SZ /d "Off" /f')
            
            # Disable VBS/HVCI
            self.log("Disabling VBS and Memory Integrity...")
            self.run_command('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\DeviceGuard" /v "EnableVirtualizationBasedSecurity" /t REG_DWORD /d 0 /f')
            self.run_command('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\DeviceGuard\\Scenarios\\HypervisorEnforcedCodeIntegrity" /v "Enabled" /t REG_DWORD /d 0 /f')
            
            # Disable UAC
            self.log("Disabling UAC...")
            self.run_command('reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v "EnableLUA" /t REG_DWORD /d 0 /f')
            
            # Apply signature bypass
            self.log("Applying signature bypass...")
            self.run_command('bcdedit /set testsigning on')
            self.run_command('bcdedit /set nointegritychecks on')
            self.run_command('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\CI\\Policy" /v "VerifiedAndReputablePolicyState" /t REG_DWORD /d 0 /f')
            
            self.log("🔴 Security DISABLED! Restart required for full effect.")
            
            if messagebox.askyesno("Restart Required", "Security features disabled!\n\nRestart now for changes to take effect?"):
                subprocess.run("shutdown /r /t 10", shell=True)
        
        threading.Thread(target=disable_thread, daemon=True).start()
    
    def enable_all_security(self):
        """Re-enable all security features for normal use."""
        if not messagebox.askyesno("Confirm", "This will RE-ENABLE all security features for normal use.\n\nContinue?"):
            return
        
        def enable_thread():
            self.log("🟢 ENABLING all security features...")
            
            # Enable Windows Defender
            self.log("Enabling Windows Defender...")
            self.run_command('reg delete "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender" /v "DisableAntiSpyware" /f')
            
            # Enable Firewall
            self.log("Enabling Windows Firewall...")
            self.run_command('netsh advfirewall set allprofiles state on')
            
            # Enable SmartScreen
            self.log("Enabling SmartScreen...")
            self.run_command('reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer" /v "SmartScreenEnabled" /t REG_SZ /d "RequireAdmin" /f')
            
            # Enable VBS/HVCI
            self.log("Enabling VBS and Memory Integrity...")
            self.run_command('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\DeviceGuard" /v "EnableVirtualizationBasedSecurity" /t REG_DWORD /d 1 /f')
            self.run_command('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\DeviceGuard\\Scenarios\\HypervisorEnforcedCodeIntegrity" /v "Enabled" /t REG_DWORD /d 1 /f')
            
            # Enable UAC
            self.log("Enabling UAC...")
            self.run_command('reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v "EnableLUA" /t REG_DWORD /d 1 /f')
            
            # Remove signature bypass
            self.log("Removing signature bypass...")
            self.run_command('bcdedit /set testsigning off')
            self.run_command('bcdedit /deletevalue nointegritychecks')
            self.run_command('reg delete "HKLM\\SYSTEM\\CurrentControlSet\\Control\\CI\\Policy" /v "VerifiedAndReputablePolicyState" /f')
            
            self.log("🟢 Security ENABLED! Restart required for full effect.")
            
            if messagebox.askyesno("Restart Required", "Security features enabled!\n\nRestart now for changes to take effect?"):
                subprocess.run("shutdown /r /t 10", shell=True)
        
        threading.Thread(target=enable_thread, daemon=True).start()
    
    def fix_signature_error(self):
        """Quick fix for signature checksum errors."""
        self.log("Fixing signature checksum errors...")
        self.run_command('bcdedit /set testsigning on')
        self.run_command('bcdedit /set nointegritychecks on')
        self.run_command('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\CI\\Policy" /v "VerifiedAndReputablePolicyState" /t REG_DWORD /d 0 /f')
        self.log("Signature error fix applied! Restart required.")
    
    def fix_invalidver_error(self):
        """Quick fix for invalidver errors."""
        self.log("Fixing invalidver errors...")
        self.run_command('w32tm /resync')
        self.run_command('reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender" /v "DisableAntiSpyware" /t REG_DWORD /d 1 /f')
        self.run_command('net stop "Windows Defender Antivirus Service"')
        self.log("Invalidver error fix applied!")
    
    def run(self):
        """Start the application."""
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleSecureToggle()
    app.run()
