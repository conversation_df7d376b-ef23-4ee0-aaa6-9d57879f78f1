@echo off
echo ===================================================
echo Automatic Secure Boot Disable Script
echo ===================================================

REM Check for Administrator privileges
NET SESSION >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Requesting Administrator privileges...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

echo Running with Administrator privileges...
echo.

echo [+] Attempting to disable Secure Boot...

REM Disable Secure Boot in registry
echo [+] Disabling Secure Boot registry entries...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\SecureBoot\State" /v "UEFISecureBootEnabled" /t REG_DWORD /d 0 /f
reg add "HKLM\SYSTEM\CurrentControlSet\Control\SecureBoot" /v "SecureBootEnabled" /t REG_DWORD /d 0 /f

REM Disable UEFI Secure Boot policies
echo [+] Disabling UEFI Secure Boot policies...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\CI\Policy" /v "SecureBootEnabled" /t REG_DWORD /d 0 /f
reg add "HKLM\SYSTEM\CurrentControlSet\Control\CI\Policy" /v "VerifiedAndReputablePolicyState" /t REG_DWORD /d 0 /f

REM Boot configuration changes
echo [+] Configuring boot settings to bypass Secure Boot...
bcdedit /set {current} testsigning on
bcdedit /set testsigning on
bcdedit /set nointegritychecks on
bcdedit /set {bootmgr} displaybootmenu yes

REM Create custom boot entry without Secure Boot
echo [+] Creating custom boot entry...
for /f "tokens=2 delims={}" %%i in ('bcdedit /create /d "Windows (No Secure Boot)" /application osloader') do set GUID=%%i
bcdedit /set {%GUID%} device partition=C:
bcdedit /set {%GUID%} path \Windows\system32\winload.exe
bcdedit /set {%GUID%} systemroot \Windows
bcdedit /set {%GUID%} testsigning on
bcdedit /set {%GUID%} nointegritychecks on
bcdedit /displayorder {%GUID%} /addfirst

REM PowerShell method to disable Secure Boot
echo [+] Attempting PowerShell Secure Boot disable...
powershell -Command "try { Confirm-SecureBootUEFI -ErrorAction Stop; Set-SecureBootUEFI -Name PK -ContentFilePath $null -ErrorAction SilentlyContinue } catch { Write-Host 'PowerShell method not available' }"

echo.
echo [+] Secure Boot disable script completed!
echo [!] RESTART REQUIRED for changes to take effect.
echo [!] If Secure Boot still shows enabled after restart,
echo [!] you may need to disable it in BIOS manually.
echo [!] Look for "Secure Boot" in BIOS Security settings.
echo.
pause
