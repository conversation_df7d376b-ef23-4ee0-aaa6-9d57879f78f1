@echo off
echo ===================================================
echo SecureToggle Build Script
echo ===================================================
echo.

REM Check for Administrator privileges
NET SESSION >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: This script requires Administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Running with Administrator privileges...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Python is not installed or not in PATH.
    echo Please install Python 3.7+ and try again.
    pause
    exit /b 1
)

echo Python found. Installing dependencies...
pip install -r requirements.txt

if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to install dependencies.
    pause
    exit /b 1
)

echo.
echo Building SecureToggle executable...
pyinstaller secure_toggle.spec

if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed.
    pause
    exit /b 1
)

echo.
echo ===================================================
echo Build Complete!
echo ===================================================
echo.
echo The SecureToggle.exe file has been created in the dist\ folder.
echo.
echo IMPORTANT NOTES:
echo 1. The executable requires administrator privileges to run
echo 2. The application is hardware-locked to this specific system
echo 3. Changes to VBS/HVCI require a system restart
echo.
echo Press any key to exit...
pause >nul
